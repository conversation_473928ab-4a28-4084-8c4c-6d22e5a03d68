# Funcionalidade de Warnings (Avisos)

## Visão Geral

A funcionalidade de Warnings permite ao webmaster gerenciar avisos através de uma interface web no sistema QIPlus. Os avisos podem ser utilizados para comunicar informações importantes aos usuários tanto por email quanto através do aplicativo.

## Localização no Sistema

- **Menu**: Planos QIPlus > Avisos
- **Rota**: `/qiplus-plans/warnings`
- **Acesso**: Restrito ao webmaster (WEBMASTER_LEVEL)

## Funcionalidades Implementadas

### 1. Listagem de Avisos
- Exibe todos os avisos cadastrados em formato de cards
- Mostra informações principais: título, conteúdo do app (resumido), datas de criação/modificação, autor e status
- Botão para criar novos avisos
- Links para visualizar e editar cada aviso
- Indicador visual de status (Ativo/Inativo)

### 2. Criação e Edição de Avisos
- Formulário completo para criar/editar avisos
- Campos obrigatórios: título, conteúdo para email, conteúdo para app
- Configurações: status ativo/inativo, autor
- Validação de formulário
- Feedback visual de sucesso/erro

## Estrutura de Dados

```javascript
{
  id: string;
  ID: string;
  title: string;
  content_mail: string;
  content_app: string;
  createdAt: timestamp;
  updatedAt: timestamp;
  collection: 'warnings';
  post_type: 'warning';
  active: boolean;
  created_by: string;
  status: 'publish';
}
```

## Arquivos Implementados

### Componentes
- `src/routes/qiplus-plans/warnings/list/index.js` - Listagem de avisos
- `src/routes/qiplus-plans/warnings/detail/index.js` - Criação/edição de avisos

### Modelo de Dados
- `src/routes/qiplus-plans/warnings/model/index.js` - Modelo e dados mockados
- `src/routes/qiplus-plans/warnings/model/fields.js` - Definição dos campos

### Configurações
- `src/components/Sidebar/NavLinks.js` - Adicionado item de menu
- `src/routes/qiplus-plans/index.js` - Configuração de rotas
- `src/lang/locales/pt_BR.js` - Traduções em português
- `src/lang/locales/en_US.js` - Traduções em inglês

## Rotas Configuradas

- `/qiplus-plans/warnings` - Listagem de avisos
- `/qiplus-plans/warnings/add` - Criar novo aviso
- `/qiplus-plans/warnings/:warningId` - Visualizar/editar aviso específico

## Dados Mockados

O sistema atualmente utiliza dados mockados para desenvolvimento e testes. Os dados incluem:

1. **Manutenção Programada do Sistema** - Aviso ativo sobre manutenção
2. **Nova Funcionalidade: Relatórios Avançados** - Anúncio de nova feature
3. **Atualização de Segurança Importante** - Aviso inativo sobre segurança
4. **Integração com WhatsApp Business API** - Anúncio de integração
5. **Política de Privacidade Atualizada** - Aviso sobre política

## Padrões Visuais

- Utiliza os cards padrão do sistema (`RctCollapsibleCard`)
- Segue a paleta de cores e tipografia existente
- Interface responsiva para diferentes tamanhos de tela
- Ícones consistentes com o design system

## Permissões

- Acesso restrito ao nível WEBMASTER_LEVEL
- Apenas usuários com permissão de webmaster podem:
  - Visualizar a listagem de avisos
  - Criar novos avisos
  - Editar avisos existentes
  - Ativar/desativar avisos

## Próximos Passos

### Integração com Firebase
- Implementar conexão com Firebase para persistência de dados
- Substituir dados mockados por queries reais
- Implementar operações CRUD completas

### Funcionalidades Adicionais
- Sistema de notificações automáticas
- Agendamento de avisos
- Templates de avisos
- Histórico de alterações
- Filtros e busca na listagem

### Melhorias de UX
- Preview do conteúdo antes de salvar
- Editor rich text para conteúdo
- Upload de imagens para avisos
- Categorização de avisos

## Tecnologias Utilizadas

- **React** (Function Components com Hooks)
- **Redux** para gerenciamento de estado
- **React Router** para roteamento
- **Reactstrap** para componentes UI
- **Material-UI** para componentes adicionais
- **Moment.js** para manipulação de datas

## Considerações Técnicas

- Componentes implementados como function components seguindo as melhores práticas
- Utilização de hooks (useState, useEffect) para gerenciamento de estado local
- Validação de formulários implementada
- Feedback visual adequado para ações do usuário
- Código organizado seguindo a estrutura do projeto existente
