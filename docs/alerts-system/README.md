# Sistema de Alertas - Warnings Collection Integration

## Visão Geral

Este documento descreve a implementação completa do sistema de alertas integrado com a coleção 'warnings' do Firebase, exibindo avisos do sistema como notificações na sidebar com listeners em tempo real e interface simplificada.

## Arquivos Modificados

### 1. `src/reducers/SidebarReducer.js`

**Modificação:** Removidos dados mock e configurado estado inicial vazio para receber dados da coleção warnings.

**Estado Inicial:** `deskNotifications: []`

### 2. `src/components/Widgets/DeskNotificationsPopper.js`

**Modificações:**
- Removidos botões de ação (deletar e visualizar)
- Simplificada interface para mostrar apenas título, mensagem e timestamp
- Mantida funcionalidade de fechar popup e marcar como visualizado

### 3. `src/actions/DeskNotificationsActions.js`

**Modificações Principais:**
- Alterada coleção de `desktop-notifications` para `warnings`
- Atualizado filtro de `qiuser` para `created_by`
- Adicionado filtro `active: true` para mostrar apenas avisos ativos
- Mapeamento de dados da estrutura warnings para formato de notificações
- Função `createTestDeskNotifications()` adaptada para criar warnings de teste

**Estrutura dos Dados Warnings (Firebase):**
```javascript
{
  // Campos originais da coleção warnings
  title: 'string',        // Título do aviso
  content_app: 'string',  // Conteúdo para app (usado como message)
  content: 'string',      // Conteúdo geral
  createdAt: number,      // Timestamp de criação
  updatedAt: number,      // Timestamp de atualização
  active: boolean,        // Se o aviso está ativo
  created_by: 'string',   // ID do usuário criador
  viewed: boolean,        // Se foi visualizado
  thumbnail: 'string',    // URL da imagem (opcional)
  url: 'string',         // URL de destino (opcional)
  locale: 'string',      // Localização
  collection: 'string'   // Nome da coleção
}
```

## Firebase Integration - Warnings Collection

### Coleção Firebase
- **Nome:** `warnings`
- **Filtros:**
  - `active: true` (apenas avisos ativos)
- **Listener:** Tempo real com `onSnapshot`
- **Ordenação:** Por `createdAt` descendente

### Funcionalidades Implementadas
1. **Listener em Tempo Real:** Alertas são atualizados automaticamente
2. **Filtro por Usuário:** Cada usuário vê apenas seus alertas
3. **Badge de Contagem:** Mostra número de alertas não visualizados
4. **Marcar como Lido:** Ao fechar o popup, alertas são marcados como visualizados
5. **Interface Limpa:** Sem botões de ação, apenas visualização

## Como Testar

### 1. Criar Warnings de Teste
No console do navegador, execute:
```javascript
window.createTestAlerts()
```
*Isso criará 3 warnings de teste na coleção 'warnings' com diferentes estados*

### 2. Visualizar Avisos como Notificações
1. Observe o badge vermelho no ícone de notificações na sidebar
2. Clique no ícone para abrir o popup
3. Veja os avisos convertidos em notificações com diferentes estados (lido/não lido)
4. Feche o popup para marcar avisos como visualizados

### 3. Verificar Dados no Firebase
- Acesse o Firebase Console
- Navegue para a coleção `warnings`
- Verifique os documentos criados com a estrutura correta

## Componentes Envolvidos

### SidebarContent.js
- Exibe o badge com contagem de alertas não visualizados
- Gerencia o clique para abrir/fechar o popup de notificações

### DeskNotificationsPopper.js
- Renderiza o popup com a lista de alertas
- Gerencia as ações de visualizar, marcar como lido e excluir
- Aplica estilos diferenciados para alertas novos vs visualizados

### SidebarReducer.js
- Armazena o estado dos alertas no Redux
- Contém os dados mock temporários

## Próximos Passos

### Funcionalidades Adicionais
1. **Interface de Administração:** Criar tela para gerenciar alertas
2. **Tipos de Alerta:** Implementar categorização (info, warning, error, success)
3. **Filtros Avançados:** Por data, tipo, status
4. **Notificações Push:** Integrar com service workers
5. **Templates:** Sistema de templates para alertas recorrentes

### Melhorias de UX
1. **Animações:** Transições suaves para novos alertas
2. **Sons:** Notificações sonoras opcionais
3. **Agrupamento:** Agrupar alertas similares
4. **Ações Rápidas:** Botões de ação contextual

## Status Atual

✅ **Implementado:**
- Firebase integration completa
- Listeners em tempo real
- Interface simplificada
- Filtro por usuário
- Badge de contagem
- Marcar como lido

⚠️ **Em Desenvolvimento:**
- Interface de administração
- Criação manual de alertas
- Tipos de alerta

## Estrutura Visual

- **Alertas Novos**: Destacados com classe CSS `new`
- **Badge**: Mostra contagem de alertas não visualizados
- **Timestamp**: Formatado usando moment.js (MOMENT_SHORT)
- **Ações**: Botões para visualizar e excluir cada alerta
- **Scroll**: Lista com scroll automático para muitos alertas

## Arquitetura Técnica

### Fluxo de Dados
1. **Firebase Listener** → `listenDeskNotifications()`
2. **Redux Action** → `LISTEN_DESK_NOTIFICATIONS_SUCCESS`
3. **Redux State** → `sidebarReducer.deskNotifications`
4. **React Component** → `DeskNotificationsPopper`
5. **UI Update** → Badge count + Popup list

### Coleção Firebase: `warnings`
```
/warnings/{warningId}
├── title: string
├── content_app: string (usado como message)
├── content: string
├── createdAt: number (timestamp)
├── updatedAt: number (timestamp)
├── active: boolean
├── created_by: string (user ID)
├── viewed: boolean
├── thumbnail: string (opcional)
├── url: string (opcional)
├── locale: string
└── collection: string
```

### Redux Actions Disponíveis
- `listenDeskNotifications()` - Listener em tempo real
- `updateDeskNotifications()` - Atualizar múltiplos alertas
- `updateDeskNotification()` - Atualizar alerta individual
- `deleteDeskNotification()` - Deletar alerta
- `createTestDeskNotifications()` - Criar dados de teste

## Troubleshooting

### Avisos não aparecem
1. Verifique se o usuário está logado
2. Execute `window.createTestAlerts()` no console
3. Verifique o console para erros do Firebase
4. Confirme se a coleção `warnings` existe
5. Verifique se os warnings têm `active: true`
6. Confirme se o `created_by` corresponde ao usuário logado

### Badge não atualiza
1. Verifique se o listener está ativo
2. Confirme se os warnings têm `viewed: false` e `active: true`
3. Verifique se o `created_by` corresponde ao usuário logado
4. Confirme se o mapeamento de dados está funcionando corretamente

### Dados não são mapeados corretamente
1. Verifique se os campos `title` e `content_app` existem nos warnings
2. Confirme se o `createdAt` é um timestamp válido
3. Verifique se a conversão de timestamp para ISO string está funcionando
