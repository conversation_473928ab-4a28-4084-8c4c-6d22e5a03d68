# Guia de Teste Manual - Seleção Múltipla de Instâncias

## Pré-requisitos

1. ✅ Aplicação QiPlus rodando localmente
2. ✅ Pelo menos 2-3 instâncias configuradas no ShotX
3. ✅ Instâncias com diferentes estados (ativas/inativas)
4. ✅ Leads associados às instâncias
5. ✅ Acesso à página de broadcast (`/shotx/broadcast`)

## Cenários de Teste

### 1. Teste de Seleção Única (Modo Padrão)

**Objetivo:** Verificar se o modo de seleção única continua funcionando normalmente.

**Passos:**
1. Acesse a página de broadcast
2. Verifique se o toggle "Seleção Múltipla" está desativado
3. Verifique se o dropdown de instâncias está visível
4. Selecione uma instância no dropdown
5. Verifique se os leads são carregados
6. Verifique se o chip da plataforma aparece

**Resultado Esperado:**
- ✅ Dropdown funciona normalmente
- ✅ Leads são carregados para a instância selecionada
- ✅ Chip da plataforma é exibido
- ✅ Interface mantém comportamento original

### 2. Teste de Alternância de Modo

**Objetivo:** Verificar se a alternância entre modos funciona corretamente.

**Passos:**
1. Na página de broadcast, ative o toggle "Seleção Múltipla"
2. Verifique se a interface muda para lista com checkboxes
3. Desative o toggle "Seleção Múltipla"
4. Verifique se volta para o dropdown original
5. Repita o processo algumas vezes

**Resultado Esperado:**
- ✅ Interface alterna corretamente entre os modos
- ✅ Seleções são limpa ao trocar de modo
- ✅ Leads são limpos ao trocar de modo
- ✅ Não há erros no console

### 3. Teste de Seleção Múltipla Básica

**Objetivo:** Verificar funcionalidade básica de seleção múltipla.

**Passos:**
1. Ative o modo "Seleção Múltipla"
2. Verifique se aparecem os botões "Selecionar todos" e "Limpar seleção"
3. Verifique se todas as instâncias aparecem na lista com checkboxes
4. Selecione uma instância clicando no checkbox
5. Verifique se o chip da instância aparece na seção "Instâncias Selecionadas"
6. Selecione uma segunda instância
7. Verifique se ambos os chips aparecem

**Resultado Esperado:**
- ✅ Lista de instâncias com checkboxes é exibida
- ✅ Botões de ação estão presentes
- ✅ Seleção individual funciona
- ✅ Chips das instâncias selecionadas aparecem
- ✅ Contador de instâncias selecionadas está correto

### 4. Teste de "Selecionar Todas"

**Objetivo:** Verificar funcionalidade de seleção em massa.

**Passos:**
1. No modo de seleção múltipla, clique em "Selecionar todos"
2. Verifique se todas as instâncias ficam selecionadas
3. Verifique se todos os chips aparecem
4. Verifique se o contador está correto

**Resultado Esperado:**
- ✅ Todas as instâncias ficam marcadas
- ✅ Todos os chips aparecem na seção de selecionadas
- ✅ Contador mostra o número total de instâncias

### 5. Teste de "Limpar Seleção"

**Objetivo:** Verificar funcionalidade de limpeza de seleção.

**Passos:**
1. Selecione algumas instâncias (ou use "Selecionar todos")
2. Clique em "Limpar seleção"
3. Verifique se todas as seleções são removidas
4. Verifique se os chips desaparecem
5. Verifique se os leads são limpos

**Resultado Esperado:**
- ✅ Todas as instâncias ficam desmarcadas
- ✅ Chips desaparecem
- ✅ Lista de leads fica vazia
- ✅ Contador de instâncias selecionadas desaparece

### 6. Teste de Remoção Individual via Chip

**Objetivo:** Verificar se é possível remover instâncias individualmente.

**Passos:**
1. Selecione 2-3 instâncias
2. Clique no "X" de um dos chips na seção "Instâncias Selecionadas"
3. Verifique se apenas aquela instância é removida
4. Verifique se o checkbox correspondente fica desmarcado
5. Repita para outros chips

**Resultado Esperado:**
- ✅ Instância é removida da seleção
- ✅ Checkbox correspondente fica desmarcado
- ✅ Chip desaparece
- ✅ Outras seleções permanecem intactas

### 7. Teste de Carregamento de Leads Múltiplos

**Objetivo:** Verificar se leads de múltiplas instâncias são carregados corretamente.

**Passos:**
1. Selecione uma instância e observe os leads carregados
2. Anote a quantidade de leads
3. Selecione uma segunda instância
4. Verifique se a quantidade de leads aumenta
5. Verifique se não há leads duplicados
6. Selecione uma terceira instância e repita

**Resultado Esperado:**
- ✅ Leads são agregados de todas as instâncias selecionadas
- ✅ Não há duplicatas na lista
- ✅ Quantidade de leads aumenta conforme esperado
- ✅ Leads têm números de telefone válidos

### 8. Teste de Validações

**Objetivo:** Verificar se as validações estão funcionando.

**Passos:**
1. No modo múltiplo, tente salvar um broadcast sem selecionar instâncias
2. Verifique se aparece mensagem de erro
3. Selecione instâncias mas não selecione leads
4. Tente salvar e verifique a validação
5. Selecione leads mas não selecione mensagem
6. Tente salvar e verifique a validação

**Resultado Esperado:**
- ✅ Mensagem de erro para instâncias não selecionadas
- ✅ Mensagem de erro para leads não selecionados
- ✅ Mensagem de erro para mensagem não selecionada
- ✅ Mensagens são claras e em português

### 9. Teste com Instâncias Inativas

**Objetivo:** Verificar tratamento de instâncias inativas.

**Passos:**
1. Certifique-se de ter pelo menos uma instância inativa
2. No modo múltiplo, selecione uma instância inativa
3. Verifique se aparece warning sobre instância inativa
4. Tente salvar o broadcast
5. Verifique se há validação adequada

**Resultado Esperado:**
- ✅ Warning sobre instância inativa é exibido
- ✅ Validação impede salvamento com instâncias inativas
- ✅ Mensagens de erro são claras

### 10. Teste de Performance

**Objetivo:** Verificar performance com múltiplas instâncias.

**Passos:**
1. Se possível, teste com 5+ instâncias
2. Selecione todas as instâncias
3. Observe o tempo de carregamento dos leads
4. Verifique se a interface permanece responsiva
5. Teste alternância rápida entre seleções

**Resultado Esperado:**
- ✅ Interface permanece responsiva
- ✅ Carregamento de leads é razoavelmente rápido
- ✅ Não há travamentos ou erros
- ✅ Memória não aumenta excessivamente

### 11. Teste de Responsividade

**Objetivo:** Verificar se a interface funciona em diferentes tamanhos de tela.

**Passos:**
1. Teste em desktop (tela grande)
2. Redimensione a janela para simular tablet
3. Teste em mobile (use dev tools)
4. Verifique se todos os elementos são acessíveis
5. Teste funcionalidades em cada tamanho

**Resultado Esperado:**
- ✅ Interface se adapta a diferentes tamanhos
- ✅ Botões permanecem clicáveis
- ✅ Texto permanece legível
- ✅ Funcionalidades funcionam em todos os tamanhos

## Checklist de Validação Final

- [ ] Modo de seleção única funciona normalmente
- [ ] Alternância entre modos funciona
- [ ] Seleção múltipla básica funciona
- [ ] "Selecionar todas" funciona
- [ ] "Limpar seleção" funciona
- [ ] Remoção individual via chip funciona
- [ ] Leads são agregados corretamente
- [ ] Validações estão funcionando
- [ ] Instâncias inativas são tratadas adequadamente
- [ ] Performance é aceitável
- [ ] Interface é responsiva
- [ ] Não há erros no console
- [ ] Traduções estão corretas
- [ ] Compatibilidade com funcionalidades existentes

## Problemas Conhecidos a Verificar

1. **Duplicação de Leads:** Verificar se leads não são duplicados entre instâncias
2. **Validação de Telefone:** Confirmar se apenas leads com telefones válidos são incluídos
3. **Estado das Instâncias:** Verificar se instâncias inativas são adequadamente sinalizadas
4. **Performance:** Monitorar performance com muitas instâncias/leads
5. **Memória:** Verificar se não há vazamentos de memória

## Relatório de Bugs

Se encontrar problemas, documente:
- **Passos para reproduzir**
- **Resultado esperado vs obtido**
- **Screenshots/videos se aplicável**
- **Console errors**
- **Ambiente (browser, OS, etc.)**
