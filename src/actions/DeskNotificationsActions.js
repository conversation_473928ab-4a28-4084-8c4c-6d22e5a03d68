/**
 * DeskNotifications Actions
 */
import { FirestoreRef } from '../firebase'

import {
  DELETE_DESK_NOTIFICATION,
  DELETE_DESK_NOTIFICATION_FAILURE,
  DELETE_DESK_NOTIFICATION_SUCCESS,
  GET_DESK_NOTIFICATIONS,
  GET_DESK_NOTIFICATIONS_FAILURE,
  GET_DESK_NOTIFICATIONS_SUCCESS,
  LISTEN_DESK_NOTIFICATIONS,
  LISTEN_DESK_NOTIFICATIONS_FAILURE,
  LISTEN_DESK_NOTIFICATIONS_SUCCESS,
  TOGGLE_DESK_NOTIFICATIONS,
  UPDATE_DESK_NOTIFICATION,
  UPDATE_DESK_NOTIFICATION_FAILURE,
  UPDATE_DESK_NOTIFICATION_SUCCESS,
  UPDATE_DESK_NOTIFICATIONS,
  UPDATE_DESK_NOTIFICATIONS_FAILURE,
  UPDATE_DESK_NOTIFICATIONS_SUCCESS,
} from 'Actions/types'

import { DEFAULT_LOCALE, MOMENT_ISO } from 'Constants'
import { WARNINGS_COLLECTION_NAME } from 'Constants/AppCollections'
import { getFormattedDate, localJSON } from 'Helpers/helpers'
import moment from 'moment'

const collectionName = WARNINGS_COLLECTION_NAME

/**
 * Redux Action Get DeskNotifications
 */
export const getDeskNotifications = queries => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  dispatch({ type: GET_DESK_NOTIFICATIONS })
  return new Promise((resolve, reject) => {
    let QueryRef = FirestoreRef.collection(collectionName)
      .where('active', '==', true)
    Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))
    QueryRef.orderBy('createdAt', 'desc')
      .get()
      .then(snapshot => {
        const posts = []
        snapshot.forEach(doc => {
          const data = doc.data()
          const createdDate = getFormattedDate(data, true)
          // Map warnings schema to desktop notifications format
          const mappedData = {
            ID: doc.id,
            title: data.title || 'Aviso do Sistema',
            message: data.content_app || data.content || '',
            scheduled_date: createdDate,
            viewed: data.viewed || false,
            thumbnail: data.thumbnail || null,
            url: data.url || null,
            created_by: data.created_by,
            active: data.active
          }
          posts.push(mappedData)
        })
        dispatch({ type: GET_DESK_NOTIFICATIONS_SUCCESS, payload: posts })
        return resolve(posts)
      })
      .catch(function (error) {
        console.error('Error fetching warnings:', error)
        dispatch({ type: GET_DESK_NOTIFICATIONS_FAILURE })
        return reject(error)
      })
  })
}

/**
 * Redux Action Get DeskNotifications
 */
export const listenDeskNotifications = (listenerFn, queries) => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  dispatch({ type: LISTEN_DESK_NOTIFICATIONS })

  let QueryRef = FirestoreRef.collection(collectionName)
    .where('active', '==', true)
  Array.isArray(queries) && queries.forEach(queryArgs => (QueryRef = QueryRef.where(queryArgs[0], queryArgs[1], queryArgs[2])))
  QueryRef = QueryRef.orderBy('createdAt', 'desc')

  return new Promise(res => {
    const refListener = QueryRef.onSnapshot(
      snapshot => {
        const posts = []
        snapshot.forEach(doc => {
          const data = doc.data()
          const createdDate = getFormattedDate(data, true)
          // Map warnings schema to desktop notifications format
          const mappedData = {
            ID: doc.id,
            title: data.title || 'Aviso do Sistema',
            message: data.content_app || data.content || '',
            scheduled_date: createdDate,
            viewed: data.viewed || false,
            thumbnail: data.thumbnail || null,
            url: data.url || null,
            created_by: data.created_by,
            active: data.active
          }
          posts.push(mappedData)
        })
        dispatch({ type: LISTEN_DESK_NOTIFICATIONS_SUCCESS, payload: posts })
        if (posts.find(p => !p.viewed)) {
          dispatch({ type: TOGGLE_DESK_NOTIFICATIONS, payload: true })
        }
        listenerFn && listenerFn(posts, refListener)
      },
      error => {
        console.error('LISTEN_DESK_NOTIFICATIONS_FAILURE', error)
        dispatch({ type: LISTEN_DESK_NOTIFICATIONS_FAILURE })
      }
    )

    return res(refListener)
  })
}

export const updateDeskNotification = (data, ID) => (dispatch, getState) => {
  const user = localJSON.get('user', false)
  dispatch({ type: UPDATE_DESK_NOTIFICATION })
  return new Promise((resolve, reject) => {
    if (!data.created_by) data.created_by = user.ID

    const updatedDeskNotification = {
      ...data,
      updatedAt: Date.now(),
      modified: moment().format(MOMENT_ISO),
      locale: data.locale || DEFAULT_LOCALE,
      collection: collectionName,
    }

    FirestoreRef.collection(collectionName)
      .doc(ID)
      .update(updatedDeskNotification)
      .then(() => {
        const payload = updatedDeskNotification
        dispatch({ type: UPDATE_DESK_NOTIFICATION_SUCCESS, payload })
        return resolve(payload)
      })
      .catch(function (error) {
        console.error('Error updating warning:', error)
        dispatch({ type: UPDATE_DESK_NOTIFICATION_FAILURE })
        return reject(error)
      })
  })
}

export const updateDeskNotifications = (deskNotifications, data) => (dispatch, getState) => {
  dispatch({ type: UPDATE_DESK_NOTIFICATIONS })
  return new Promise((resolve, reject) => {
    let updatedData = deskNotifications.map(n => ({
      ...(data || {}),
      ID: n.ID,
      updatedAt: Date.now(),
      modified: moment().format(MOMENT_ISO),
      locale: n.locale || DEFAULT_LOCALE,
      collection: collectionName,
    }))

    const updatedPosts = deskNotifications.map((n, i) => ({
      ...n,
      ...updatedData[i],
    }))

    if (updatedData.length) {
      const batch = FirestoreRef.batch()

      updatedData.forEach(d => {
        let docRef = FirestoreRef.collection(collectionName).doc(d.ID)
        batch.update(docRef, d)
      })

      return batch
        .commit()
        .then(() => {
          const payload = updatedPosts
          dispatch({ type: UPDATE_DESK_NOTIFICATIONS_SUCCESS, payload })
          return resolve(payload)
        })
        .catch(function (error) {
          console.error('Error updating warnings:', error)
          dispatch({ type: UPDATE_DESK_NOTIFICATIONS_FAILURE })
          return reject(error)
        })
    }

    return resolve(deskNotifications)
  })
}

export const deleteDeskNotification = nId => (dispatch, getState) => {
  dispatch({ type: DELETE_DESK_NOTIFICATION })
  return new Promise((resolve, reject) => {
    FirestoreRef.collection(collectionName)
      .doc(`${nId}`)
      .delete()
      .then(() => {
        dispatch({ type: DELETE_DESK_NOTIFICATION_SUCCESS, ID: nId })
        return resolve(nId)
      })
      .catch(function (error) {
        dispatch({ type: DELETE_DESK_NOTIFICATION_FAILURE, ID: nId })
        return reject(error)
      })
  })
}
