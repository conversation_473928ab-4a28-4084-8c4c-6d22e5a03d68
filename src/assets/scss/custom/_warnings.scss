/*============ Warnings Styles =============*/

// Estilos específicos para a funcionalidade de Warnings
.warnings-detail {
  .editor-container {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box;

    .ckeditor-wrapper,
    .simple-editor-wrapper {
      width: 100% !important;
      max-width: 100% !important;
      border: none !important;

      .cke {
        width: 100% !important;
        max-width: 100% !important;
        border: none !important;
        box-shadow: none !important;

        .cke_inner {
          width: 100% !important;
          max-width: 100% !important;

          .cke_contents {
            width: 100% !important;
            max-width: 100% !important;

            .cke_wysiwyg_frame {
              width: 100% !important;
              max-width: 100% !important;
              margin: 0 !important;
            }
          }
        }
      }
    }
  }

  // Estilos para responsividade dinâmica
  .dynamic-height-card {
    transition: min-height 0.3s ease-in-out !important;
    overflow: visible !important;

    .rct-block-content {
      transition: height 0.3s ease-in-out;
      overflow: visible !important;
    }

    // Quando o editor está ativo
    &.editor-active {
      .rct-block-content {
        min-height: inherit !important;
        height: auto !important;
        overflow: visible !important;
      }

      .rct-block {
        min-height: inherit !important;
        height: auto !important;
      }
    }
  }

  .dynamic-editor-container {
    transition: min-height 0.3s ease-in-out !important;
    overflow: visible !important;

    .ckeditor-wrapper,
    .simple-editor-wrapper {
      transition: height 0.3s ease-in-out;
      overflow: visible !important;

      .cke {
        transition: height 0.3s ease-in-out;
        overflow: visible !important;

        .cke_contents {
          transition: height 0.3s ease-in-out;
          overflow-y: auto !important;
          overflow-x: hidden !important;
        }
      }
    }
  }

  // Melhorias para redimensionamento automático
  .dynamic-content {
    overflow: visible !important;
    min-height: inherit;

    .form-group {
      margin-bottom: 0;

      .editor-container {
        margin-bottom: 15px;
      }
    }
  }

  // Estilos para as abas
  .MuiTabs-root {
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;

    .MuiTab-root {
      min-width: 120px;
      font-weight: 500;
      text-transform: none;

      &.Mui-selected {
        color: #5d78ff;
        font-weight: 600;
      }
    }

    .MuiTabs-indicator {
      background-color: #5d78ff;
      height: 3px;
    }
  }

  // Responsividade para diferentes tamanhos de tela
  @media (max-width: 1200px) {
    .dynamic-height-card {
      .rct-block-content {
        padding: 15px;
      }
    }

    .dynamic-editor-container {
      min-height: 300px !important;
    }
  }

  @media (max-width: 768px) {
    .editor-container {
      .ckeditor-wrapper,
      .simple-editor-wrapper {
        .cke {
          .cke_top {
            overflow-x: auto;
          }
        }
      }
    }

    .dynamic-height-card {
      margin-bottom: 15px;

      .rct-block-content {
        padding: 10px;
      }
    }

    .dynamic-editor-container {
      min-height: 250px !important;
      border-radius: 6px;
    }
  }

  @media (max-width: 480px) {
    .dynamic-editor-container {
      min-height: 200px !important;

      .ckeditor-wrapper,
      .simple-editor-wrapper {
        .cke {
          .cke_top {
            .cke_toolbar {
              flex-wrap: wrap;
            }
          }
        }
      }
    }
  }
}

// Fix global para CKEditor em containers responsivos
body[data-section] {
  .rct-block-content {
    .editor-container {
      .ckeditor-wrapper,
      .simple-editor-wrapper {
        width: 100% !important;
        max-width: 100% !important;

        .cke {
          width: 100% !important;
          max-width: 100% !important;

          .cke_inner {
            width: 100% !important;

            .cke_contents {
              width: 100% !important;

              .cke_wysiwyg_frame {
                width: 100% !important;
                max-width: 100% !important;
              }
            }
          }
        }
      }
    }
  }
}

// Estilos para cards de warnings na listagem
.warning-card-content {
  .warning-meta {
    border-top: 1px solid #f0f0f0;
    padding-top: 15px;
    margin-top: 15px;

    small {
      display: block;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      i {
        width: 16px;
        text-align: center;
        opacity: 0.7;
      }
    }
  }

  .warning-actions {
    .btn {
      font-size: 0.875rem;
      padding: 6px 12px;

      i {
        font-size: 0.75rem;
      }
    }
  }
}

// Badge de status
.badge {
  &.badge-success {
    background-color: #28a745;
  }

  &.badge-secondary {
    background-color: #6c757d;
  }
}
