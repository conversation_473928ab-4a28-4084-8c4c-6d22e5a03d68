import COLLECTIONS from "Constants/AppCollections";
import { FirebaseRepository } from "FirebaseRef/repository";

export class AccountRepository {
    accountCollection = COLLECTIONS.ACCOUNTS_COLLECTION_NAME;
    qiUserCollection = COLLECTIONS.QIUSERS_COLLECTION_NAME;

    constructor(accountId) {
        this.accountId = accountId
        this.repository = new FirebaseRepository()
        this.path = `${this.accountCollection}`
    }

    getAccount = (onSuccess, onError) => {
        if (!this.accountId) {
            onError('Account ID is required')
            return
        }
        return this.repository.getDoc(`${this.path}/${this.accountId}`, onSuccess, onError)
    }

    findByOwner = async (ownerId) => {
        const accounts = await this.repository.getDocs(`${this.path}`, [['owner', '==', ownerId]], '', '', 1)
        if (accounts.length > 0) {
            return accounts[0]
        }
        return null
    }

    findUser = async () => {
        const qiUsers = await this.repository.getDocs(`${this.qiUserCollection}`, [['accountId', '==', this.accountId]], '', '', 1)
        if (qiUsers.length > 0) {
            return qiUsers[0]
        }
        return null
    }

    findUserByOwner = async (ownerId) => {
        const qiUsers = await this.repository.getDocs(`${this.qiUserCollection}`, [['owner', '==', ownerId]], '', '', 1)
        if (qiUsers.length > 0) {
            return qiUsers[0]
        }
        return null
    }
}
