const AppModules = require('../../constants/AppModules')
const AppTaxonomies = require('../../constants/AppTaxonomies')

const en_US_Strings = {
  "accounts.account": "Account",
  "accounts.accountSettings": "Account Settings",
  "accounts.accountType": "Account Type",
  "accounts.brand": "Brand",
  "accounts.corporation": "Corporation",
  "accounts.createAccountOny": "Create Account Only",
  "accounts.createSubscription": "Create Subscription",
  "accounts.createSubscriptionOnSave":
    "Do you want to create a subscription now? If the account has an affiliate, inform the affiliate before creating the first subscription",
  "accounts.hasPendingUpdate":
    "Your account has a pending upgrade. Complete the payment to activate it",
  "accounts.inactiveAccount": "Inactive",
  "accounts.individual": "Individual",
  "accounts.invoice": "Invoice",
  "accounts.invoices": "Invoices",
  "accounts.mainUser": "Main account user",
  "accounts.noLeadsOptions":
    "The plan does not allow selecting the number of leads",
  "accounts.parentAccount": "Parent Account",
  "accounts.qtdLeads": "Number of Leads",
  "accounts.settings": "Settings",
  "accounts.slogan": "Slogan",
  "accounts.updateMyPlan": "Update My Plan",
  "accounts.updatePlan": "Update Plan",
  "accounts.updateSubscription": "Update Subscription",
  "accounts.upgrade": "Plan Upgrade",
  "actions.add.brodcast": "Add Broadcast Message",
  "actions.addConditions": "Add Conditions",
  "actions.addCustomField": "Create Custom Field",
  "actions.addDealTags": "Add Tags to Deal",
  "actions.addField": "Add Field",
  "actions.addLeadTags": "Add Tags to Contact",
  "actions.addNewDeal": "Create a Deal",
  "actions.addScore": "Add Scoring",
  "actions.addTags": "Add Tags",
  "actions.addTasklist": "Add Task List",
  "actions.addToCampaign": "Add to Campaign",
  "actions.addToEvent": "Add to Event",
  "actions.addToFunnel": "Add to Funnel",
  "actions.addToSegmentation": "Add to Segmentation",
  "actions.addToStore": "Add to Store",
  "actions.assign": "Assign",
  "actions.assignManager": "Assign Account Manager",
  "actions.assignProfile": "Assign Profile",
  "actions.assignSeller": "Assign Seller",
  "actions.assignTasklistToDeal": "Assign Tasks",
  "actions.assignTasklistToTeam": "Assign Tasks to Team",
  "actions.assignTasklistToUser": "Assign Tasks to User",
  "actions.assignTeam": "Assign Team",
  "actions.assignUser": "Assign User",
  "actions.back": "Back",
  "actions.changeFunnel": "Change Funnel",
  "actions.changeToFunnel": "Switch to Funnel",
  "actions.conditions": "Conditions",
  "actions.continue": "Continue",
  "actions.createEmbed": "Create Embed",
  "actions.createModel": "Create Model",
  "actions.createShortlink": "Create Shortlink",
  "actions.createTicket": "Generate Order",
  "actions.dealWebhook": "Send Deal via Webhook",
  "actions.deleteConditions": "Delete Conditions",
  "actions.generateContract": "Generate Contract",
  "actions.gotoAction": "Jump to Another Action",
  "actions.gotoAutomation": "Start Another Automation",
  "actions.gotoReset": "Click to reset linked action",
  "actions.gotoTooltip": "Drag the target to the desired action",
  "actions.importCustomField": "Import Custom Field",
  "actions.instructions.updateDeal":
    "Updates an existing Deal in the selected Funnel",
  "actions.manageAttachments": "Edit Attachments",
  "actions.newImport": "New Import",
  "actions.other": "Other Actions",
  "actions.preview": "Preview",
  "actions.removeDealTags": "Remove Deal Tags",
  "actions.removeLeadTags": "Remove Contact Tags",
  "actions.removeTags": "Remove Tags",
  "actions.saveConditions": "Save Conditions",
  "actions.schedule": "Schedule",
  "actions.scheduled": "Scheduled",
  "actions.selectContacTitle": "Select a Contact",
  "actions.sendBroadcast": "Send Broadcast",
  "actions.sendContract": "Send Contract",
  "actions.sendEmail": "Send Email",
  "actions.sendMessage": "Send Message",
  "actions.sendNotificationToManager": "Notify Account Manager",
  "actions.sendNotificationToSeller": "Notify Seller",
  "actions.sendTestEmail": "Send Test Email",
  "actions.sendZap": "QIPlus Zap",
  "actions.sendZapToSeller": "QIPlus Zap - Seller",
  "actions.shotxSendMessageText": "Send Text Message",
  "actions.stageBackInFunnel": "Advance in Funnel",
  "actions.stageForwardInFunnel": "Move Back in Funnel",
  "actions.testEmail": "Send Test",
  "actions.testImapUser": "Test IMAP Settings",
  "actions.testSMTPUser": "Test SMTP Settings",
  "actions.timer": "Timer",
  "actions.updateDeal": "Update Deal",
  "actions.webhook": "Send Contact via Webhook",
  "affiliate.accountType.conta_corrente": "Checking Account",
  "affiliate.accountType.conta_corrente_conjunta": "Joint Checking Account",
  "affiliate.accountType.conta_poupanca": "Savings Account",
  "affiliate.accountType.conta_poupanca_conjunta": "Joint Savings Account",
  "affiliate.commission": "Commission",
  "affiliate.commissions": "Commissions",
  "affiliate.data": "Affiliate Data",
  "affiliate.data.document_number": "Document",
  "affiliate.data.email": "Email",
  "affiliate.data.name": "Full Name",
  "affiliate.data.phone_numbers": "Mobile Phone",
  "affiliate.data.site_url": "Website",
  "affiliate.data.type": "Account Type",
  "affiliate.termLink": "Affiliation Terms Link",
  "affiliate.transfer.auto": "Automatic Payment",
  "affiliate.transfer.daily": "Daily",
  "affiliate.transfer.day": "Payment Day",
  "affiliate.transfer.interval": "Payment Frequency",
  "affiliate.transfer.monthly": "Monthly",
  "affiliate.transfer.weekly": "Weekly",

  "affiliates.deleteConfirmationBody":
    "The affiliate will be permanently deleted",
  "affiliates.deleteConfirmationTitle":
    "Are you sure you want to delete this affiliate?",
  "affiliates.duplicatedMsg": "An affiliate with this data already exists",
  "affiliates.switchToPost": "Do you want to edit the affiliate?",

  "alerts.accountWillBeSavedBefore":
    "Attention, the account will be saved with the current settings before performing this operation",
  "alerts.actionsWouldBeDeleted":
    "There are actions in the automation that would be lost by deleting this step. Remove the actions or reassign them before deleting the step",
  "alerts.atention": "Attention",
  "alerts.cardUpdateNotAllowed":
    "You cannot change the card while the subscription is active",
  "alerts.changeModel":
    "Changing the template will discard edited content. Do you wish to continue?",
  "alerts.contentWasModifiedByUser":
    "The content being edited was modified by another user",
  "alerts.contentWasModifiedInDB":
    "The content was modified by another user or automation in the database",
  "alerts.doingCheckout":
    "We are setting up your account. Please keep the browser window open until checkout is complete.",
  "alerts.dontHaveData": "No data to display",
  "alerts.emailAlreadyExists":
    "A user with this email already exists. Use another email or recover your account.",
  "alerts.emailChangeNotAllowed":
    "Email change is not allowed for this account",
  "alerts.emailExists": "A user with this email already exists.",
  "alerts.emailSMTPSuccess": "SMTP settings configured successfully",
  "alerts.emailTestFailed": "Failed to send test email",
  "alerts.emailTestSuccess": "Test email sent successfully!",
  "alerts.emptyAccountOwner": "Please provide the main account user",
  "alerts.emptyDisplayName": "Please provide a display name",
  "alerts.emptyEmailAddress": "Please provide an email address",
  "alerts.emptyFirstName": "Please provide a first name",
  "alerts.emptyRecipients": "Select recipients",
  "alerts.emptyRoles": "Assign at least one role to the user",
  "alerts.emptyStart": "Please provide a start date",
  "alerts.emptyTitle": "Please provide a title",
  "alerts.imapUserAuthError": "Failed to establish IMAP connection",
  "alerts.imapUserAuthSuccess": "IMAP connection established successfully",
  "alerts.informCountryCode": "Please provide country code and area code",
  "alerts.informFullPhoneNumber":
    "Please provide phone number with country and area code",
  "alerts.invalidPassword": "Invalid password",
  "alerts.itemsLimitReached": "Item limit reached",
  "alerts.mailboxNotSchedulable":
    "Sending via inbox does not allow future scheduling or sending to the entire lead base. The email will be sent immediately and won’t appear in the sending list",
  "alerts.minLengthPassord6": "Password must be at least 6 characters",
  "alerts.missingLoginData": "Invalid username or password",
  "alerts.mobileViewportDetected":
    "You are using a screen resolution lower than recommended. Minimum suggested resolution is 1280x800",
  "alerts.noChangesToSave": "No changes to save",
  "alerts.notAccountOwner":
    "This content does not belong to the current account",
  "alerts.onChangeBillingData":
    "Changing billing data will generate a new subscription starting today upon checkout.",
  "alerts.onlyUpgradeAllowed":
    "The new plan value must be higher than the current plan to proceed with checkout on an active subscription",
  "alerts.operationWillBeAborted":
    "The current operation will be lost and cannot be recovered",
  "alerts.phoneAlreadyExists":
    "A user with this phone number already exists. Use another number or recover your account.",
  "alerts.plaeaseRelateFields": "Please select fields to associate",
  "alerts.pleaseCompletePersonalInfo": "Please complete your registration",
  "alerts.pleaseInformValidCPF": "Please enter a valid CPF",
  "alerts.pleaseInformYourCPF": "Please enter your CPF",
  "alerts.pleaseInformYourDisplayName": "Please enter your username",
  "alerts.pleaseInformYourEmail": "Please enter your email",
  "alerts.pleaseInformYourEmailAndPassword": "Please enter email and password",
  "alerts.pleaseInformYourMobileNumber": "Please enter your mobile number",
  "alerts.pleaseInformYourPassword": "Please enter your password",
  "alerts.resetViewsConfirm":
    "This will reset the view count. Are you sure you want to continue?",
  "alerts.subscriptionWilBeUpdated":
    "Upon checkout, your account will be updated and only the prorated amount for the new plan will be charged, without changing renewal dates or previous charges.",
  "alerts.tooManyRequests":
    "You have exceeded the maximum login attempts. Please try again later",
  "alerts.updateToBoletoNotAllowed":
    "It is not possible to change payment from boleto to credit card",
  "alerts.userAlreadyExists":
    "A user with this information already exists. Create a new user or recover the account.",
  "alerts.userIsNotActive": "Oops, your user is currently inactive",
  "alerts.userNotRegistered":
    "You don’t have an active user in QIPlus yet. Register now or contact us to learn more.",
  "alerts.validateYourEmailAddress": "Validate your email before proceeding",
  "alerts.verifyFormErrors": "Check form errors before proceeding",
  "alerts.weakPassword": "Choose a password with at least 6 characters",
  "alerts.welcomeToAffiliateProgram": "Welcome to the QIPlus affiliate program",
  "alerts.wrongPassword": "Invalid password",
  "alerts.youMightLooseYourChanges":
    "Some changes may be lost due to external modifications",

  "app.appName": "QIPlus",
  "app.slogan": "Turn your customers into fans",
  "app.sloganAction": "Start turning your customers into fans now!",
  "app.updatedMsg": "Request processed successfully!",
  "app.welcome": "Welcome to QIPlus!",

  "ask.displayName": "How would you like to be called?",

  "attachments.download": "Download",
  "attachments.downloadAll": "Download all attachments",

  "automations.fired": "Triggers",
  "automations.lastActionAlert":
    "This action can only be added at the end of a sequence",
  "automations.loopAlert":
    "You cannot add the same item to both triggers and automation.",
  "automations.possibleLoopDanger":
    "This action cannot be performed as it may cause an infinite loop.",
  "button.4Steps": "Review the 4 steps to start your sales operation",
  "button.accept": "Accept",
  "button.acceptTerms": "Accept Terms",
  "button.add": "Add",
  "button.addNew": "New",
  "button.addNewUser": "Add User",
  "button.addOption": "Add Option",
  "button.addOptions": "Add Options",
  "button.addTriggerGroup": "New Rule Set",
  "button.assignNow": "Assign Now",
  "button.associate": "Associate to Lead",
  "button.back": "Back",
  "button.blockLevelButton": "Block Level Button",
  "button.button": "Button",
  "button.cancel": "Cancel",
  "button.click": "Click",
  "button.clone": "Clone",
  "button.close": "Close",
  "button.complete": "Complete",
  "button.completeStep": "Complete Step",
  "button.confirm": "Confirm",
  "button.connect": "Connect",
  "button.convert": "Convert",
  "button.copy": "Copy",
  "button.create": "Create",
  "button.createNewLead": "Create New Lead",
  "button.cropImage": "Crop Image",
  "button.danger": "Danger",
  "button.delete": "Delete",
  "button.discard": "Discard",
  "button.disconnectAccount": "Disconnect Account",
  "button.downloadPdfReport": "Download PDF Report",
  "button.edit": "Edit",
  "button.editOption": "Edit Option",
  "button.editOptions": "Edit Options",
  "button.editTicket": "Edit Order",
  "button.error": "Error",
  "button.exclude": "Exclude",
  "button.exportToExcel": "Export to Excel",
  "button.goToCampaign": "Go to Campaign",
  "button.hide": "Hide",
  "button.import": "Import",
  "button.info": "Info",
  "button.largeButton": "Large Button",
  "button.learnMore": "Learn More",
  "button.leave": "Logout",
  "button.like": "Like",
  "button.link": "Link",
  "button.lost": "Lost",
  "button.more": "More",
  "button.newLead": "New Lead",
  "button.newOption": "New Option",
  "button.newOptions": "New Options",
  "button.next": "Next",
  "button.nfConfirm": "Issue Invoice",
  "button.nfConfirmNotazz": "Issue Invoice on Notazz",
  "button.no": "No",
  "button.ok": "Ok",
  "button.openMenu": "Open Menu",
  "button.openPopover": "Open Popover",
  "button.openWithFadeTransition": "Open With Fade Transition",
  "button.pen": "Pen",
  "button.preview": "Preview",
  "button.primary": "Primary",
  "button.primaryButton": "Primary Button",
  "button.reassignUsers": "Assign Account",
  "button.reject": "Reject",
  "button.remove": "Remove",
  "button.removeAll": "Remove All",
  "button.removeTriggerGroup": "Remove Rule Set",
  "button.reply": "Reply",
  "button.reset": "Clear",
  "button.resetField": "Clear Field",
  "button.resetFilters": "Clear Filters",
  "button.resetStyles": "Reset Styles",
  "button.resetViews": "Reset Views",
  "button.save": "Save",
  "button.saveAndContinue": "Save and Continue Editing",
  "button.saveAsDraft": "Save as Draft",
  "button.saveChanges": "Save Changes",
  "button.saveDraft": "Save Draft",
  "button.saveNow": "Save Now",
  "button.search": "Search",
  "button.secondary": "Secondary",
  "button.seeInsights": "See Insights",
  "button.select": "Select",
  "button.send": "Send",
  "button.sendMessage": "Send Message",
  "button.sendTicket": "Complete Order",
  "button.settings": "Settings",
  "button.show": "Show",
  "button.signIn": "Sign In",
  "button.signUp": "Sign Up",
  "button.smallButton": "Small Button",
  "button.success": "Success",
  "button.support": "Open Support Ticket",
  "button.tryAgain": "Try Again",
  "button.understood": "Understood",
  "button.undo": "Undo",
  "button.undoExternalChanges": "Undo External Changes",
  "button.update": "Update",
  "button.useDefaultImage": "Use Default Image",
  "button.view": "View",
  "button.viewAll": "View All",
  "button.viewLess": "View Less",
  "button.viewMore": "View More",
  "button.viewProfile": "View Profile",
  "button.warning": "Warning",
  "button.won": "Won",
  "button.writeNewMessage": "New Message",
  "button.yes": "Yes",
  "calendar.agenda": "Schedule",
  "calendar.agendas": "Schedules",
  "calendar.allDay": "All Day",
  "calendar.date": "Date",
  "calendar.dates": "Dates",
  "calendar.day": "Day",
  "calendar.days": "Days",
  "calendar.deleteConfirmationBody": "The event will be permanently deleted",
  "calendar.deleteConfirmationTitle":
    "Are you sure you want to delete this event?",
  "calendar.event": "Event",
  "calendar.hour": "Hour",
  "calendar.hours": "Hours",
  "calendar.info": "Event Details",
  "calendar.minute": "Minute",
  "calendar.minutes": "Minutes",
  "calendar.month": "Month",
  "calendar.month.abr": "April",
  "calendar.month.ago": "August",
  "calendar.month.dez": "December",
  "calendar.month.fev": "February",
  "calendar.month.jan": "January",
  "calendar.month.jul": "July",
  "calendar.month.jun": "June",
  "calendar.month.mai": "May",
  "calendar.month.mar": "March",
  "calendar.month.nov": "November",
  "calendar.month.out": "October",
  "calendar.month.set": "September",
  "calendar.months": "Months",
  "calendar.newEvent": "New Event",
  "calendar.next": "Next",
  "calendar.previous": "Previous",
  "calendar.second": "Second",
  "calendar.seconds": "Seconds",
  "calendar.time": "Time",
  "calendar.today": "Today",
  "calendar.tomorrow": "Tomorrow",
  "calendar.updateEvent": "Edit Event",
  "calendar.week": "Week",
  "calendar.weeks": "Weeks",
  "calendar.work_week": "Workdays",
  "calendar.year": "Year",
  "calendar.years": "Years",
  "calendar.yesterday": "Yesterday",

  "campaigns.campaignHomeUrl": "Campaign Main URL",

  "campaigns.hunter.actions": "Actions",
  "campaigns.hunter.analytics.clickDetails.byElement": "Clicks by Element",
  "campaigns.hunter.analytics.clickDetails.byElementDescription":
    "Select an element to view click details",
  "campaigns.hunter.analytics.clickDetails.byElementId":
    "Clicks by ID in :element",
  "campaigns.hunter.analytics.clickDetails.title": "Click Details",
  "campaigns.hunter.analytics.eventsEvolution.title": "Event Evolution",
  "campaigns.hunter.analytics.filters": "Filters",
  "campaigns.hunter.analytics.filters.byQueryParamName":
    "Filter by Parameter Name",
  "campaigns.hunter.analytics.filters.byQueryParamValue":
    "Filter by Parameter Value",
  "campaigns.hunter.analytics.filters.byRoute": "Filter by Route",
  "campaigns.hunter.analytics.filters.byRouteTooltip":
    "Filter routes accessed during the selected period",
  "campaigns.hunter.analytics.filters.clear": "Clear",
  "campaigns.hunter.analytics.filters.loading": "Loading...",
  "campaigns.hunter.analytics.filters.noOptions": "No filters available",
  "campaigns.hunter.analytics.filters.open": "Open",
  "campaigns.hunter.analytics.noClicks": "No clicks recorded",
  "campaigns.hunter.analytics.noData": "No data to display",
  "campaigns.hunter.analytics.title": "QI Hunter Metrics",
  "campaigns.hunter.cancel": "Cancel",
  "campaigns.hunter.clone": "Copy",
  "campaigns.hunter.close": "Close",
  "campaigns.hunter.code": "QI Hunter Code",
  "campaigns.hunter.codeEnd": "End of QI Hunter",
  "campaigns.hunter.codeStart": "Start of QI Hunter",
  "campaigns.hunter.copied": "Copied to clipboard",
  "campaigns.hunter.copy": "Copy",
  "campaigns.hunter.create": "Create QI Hunter",
  "campaigns.hunter.createFailed": "Failed to create QI Hunter",
  "campaigns.hunter.created": "QI Hunter created successfully",
  "campaigns.hunter.createdAt": "Created on",
  "campaigns.hunter.creating": "Creating QI Hunter",
  "campaigns.hunter.delete": "Delete",
  "campaigns.hunter.description": "Create and manage your tracking pixels",
  "campaigns.hunter.domain": "Domain",
  "campaigns.hunter.duplicate": "Duplicate",
  "campaigns.hunter.edit": "Edit QI Hunter",
  "campaigns.hunter.events.clicked": "Clicks",
  "campaigns.hunter.events.empty": "No events selected for tracking",
  "campaigns.hunter.events.select": "Events to Track",
  "campaigns.hunter.events.title": "Events",
  "campaigns.hunter.events.viewed": "Views",
  "campaigns.hunter.events.warning":
    "Copy and update the QI Hunter code to apply changes.",
  "campaigns.hunter.gridView": "Grid View",
  "campaigns.hunter.listView": "List View",
  "campaigns.hunter.name": "QI Hunter Name",
  "campaigns.hunter.new": "New QI Hunter",
  "campaigns.hunter.newDescription": "QI Hunter Description",
  "campaigns.hunter.stats": "Statistics",
  "campaigns.hunter.status": "Status",
  "campaigns.hunter.statusDisabled": "Disabled",
  "campaigns.hunter.statusEnabled": "Enabled",
  "campaigns.hunter.statusToggle": "Enable/Disable",
  "campaigns.hunter.tags": "Tags",
  "campaigns.hunter.title": "QI Hunter",
  "campaigns.hunter.update": "Update QI Hunter",
  "campaigns.hunter.updateFailed": "Failed to update QI Hunter",
  "campaigns.hunter.updated": "QI Hunter updated successfully",
  "campaigns.hunter.updatedAt": "Updated on",

  "campaigns.performance": "Campaign Performance",

  "cart.deleteWarning": "Changes to the Order will be lost",

  "chat.contactInfo": "Contact Info",
  "chat.deleteGroup": "Delete Group",
  "chat.filterContacts": "Filter Contacts",
  "chat.group": "Groups",
  "chat.leaveChat": "Delete Conversation",
  "chat.leaveGroup": "Leave Group",
  "chat.newGroup": "New Group",
  "chat.noContactsSelected": "No contacts selected",
  "chat.renameGroup": "Rename Group",
  "chat.selectContacts": "Select Contacts",
  "chat.sendToMultipleRecipients": "Send to Multiple Recipients",
  "chat.startChatTip": "Choose a contact or user to start a conversation",
  "chat.startChatTip2": "Test",
  "chat.typeAContactName": "Type contact name",
  "chat.typeAGroupName": "Type group name",
  "chat.typeAMessage": "Write a message",
  "chat.writeAMessage": "Write your message",

  "checklist.add": "New Checklist",
  "checklist.clone": "Clone Checklist",
  "checklist.create": "Create Checklist",
  "checklist.edit": "Edit Checklist",
  "checklists.clone": "Clone Checklists",
  "checklists.edit": "Edit Checklists",

  "commissions.canceled": "Canceled",
  "commissions.ended": "Finished",
  "commissions.paid": "Paid",
  "commissions.pending_payment": "Pending",
  "commissions.trialing": "Trial",
  "commissions.unpaid": "Unpaid",

  "components.CartEmptyText": "The shopping cart is empty",
  "components.NoItemFound": "No items found",
  "components.ShippingAddressText":
    "Shipping address is the same as billing address.",
  "components.addNewTasks": "New Task",
  "components.addToCart": "Add to Cart",
  "components.address": "Address",
  "components.address2Optional": "Address 2 (Optional)",
  "components.advancedSettings": "Advanced Settings",
  "components.ageRange": "Age Range",
  "components.all": "All",
  "components.approve": "Approve",
  "components.automation": "Automation",
  "components.automations": "Automations",
  "components.basic": "Basic",
  "components.basicChip": "Basic Chip",
  "components.basicFeatures": "Basic Features",
  "components.billingAddress": "Billing Address",
  "components.billingData": "Billing Data",
  "components.buyNow": "Buy Now",
  "components.cancelled": "Cancelled",
  "components.cardNumber": "Card Number",
  "components.cart": "Shopping Cart",
  "components.changeBillingData": "Change Billing Data",
  "components.checkout": "Checkout",
  "components.choose": "Choose",
  "components.city": "City",
  "components.clickableChip": "Clickable Chip",
  "components.companyName": "Company Name",
  "components.compose": "Compose",
  "components.confirmPasswords": "Confirm Password",
  "components.country": "Country",
  "components.customIcon": "Custom Icon",
  "components.customStyle": "Custom Style",
  "components.cvv": "CVV",
  "components.data": "Data",
  "components.do": "Do",
  "components.done": "Done",
  "components.dontHaveAccountSignUp": "Don't Have an Account? Sign Up",
  "components.drafts": "Drafts",
  "components.email": "Email",
  "components.emailPrefrences": "Email Preferences",
  "components.enterEmailAddress": "Enter Email Address",
  "components.enterUserName": "Enter Username",
  "components.expansionPanel1": "Expansion Panel 1",
  "components.expansionPanel2": "Expansion Panel 2",
  "components.expiryDate": "Expiry Date",
  "components.facebookAds": "Facebook Ads",
  "components.facebookReports": "Facebook Reports",
  "components.firstName": "First Name",
  "components.followers": "Followers",
  "components.funnelActions": "Actions",
  "components.generalSetting": "General Settings",
  "components.goToHomePage": "Go to Home Page",
  "components.goToShop": "Go to Shop",
  "components.integration": "Integration",
  "components.integrations": "Integrations",
  "components.item": "Item",
  "components.items": "Items",
  "components.last1Month": "Last Month",
  "components.last6Month": "Last 6 Months",
  "components.last7Days": "Last 7 Days",
  "components.lastName": "Last Name",
  "components.leadEntrances": "Lead Entry",
  "components.left": "Left",
  "components.mobileNumber": "Mobile Number",
  "components.month": "Month",
  "components.myProfile": "My Profile",
  "components.nameOnCard": "Name on Card",
  "components.occupation": "Occupation",
  "components.openAlert": "Open Alert",
  "components.openFullScreenDialog": "Open Full Screen Dialog",
  "components.pageNotfound": "Page Not Found",
  "components.paid": "Paid",
  "components.passwordPrompt": "Password Prompt",
  "components.passwords": "Passwords",
  "components.payNow": "Pay Now",
  "components.payment": "Payment",
  "components.pending": "Pending",
  "components.persistentDrawer": "Persistent Drawer",
  "components.phoneNo": "Phone",
  "components.pipeline": "Pipeline",
  "components.pipelines": "Pipelines",
  "components.placeOrder": "Place Order",
  "components.popularity": "Popularity",
  "components.print": "Print",
  "components.product": "Product",
  "components.projectName": "Project Name",
  "components.prompt": "Prompt",
  "components.quantity": "Quantity",
  "components.refunded": "Refunded",
  "components.removeProduct": "Remove Product",
  "components.right": "Right",
  "components.saveContinue": "Save and Continue",
  "components.sendMessage": "Send Message",
  "components.sent": "Sent",
  "components.settings": "Settings",
  "components.signIn": "Sign In",
  "components.slideInAlertDialog": "Slide In Alert Dialog",
  "components.social Connection": "Social Connection",
  "components.sorryServerGoesWrong":
    "Sorry, something went wrong with the server",
  "components.spaceUsed": "Space Used",
  "components.state": "State",
  "components.submit": "Submit",
  "components.success": "Success",
  "components.summary": "Summary",
  "components.task_question": "Questionnaire",
  "components.tasklist": "Task",
  "components.tasklists": "Tasks",
  "components.title": "Title",
  "components.today": "Today",
  "components.totalPrice": "Total Price",
  "components.trash": "Trash",
  "components.trending": "Trending",
  "components.unlock": "Unlock",
  "components.username": "Username",
  "components.viewCart": "View Shopping Cart",
  "components.warning": "Warning",
  "components.week": "Week",
  "components.withDescription": "With Description",
  "components.withHtml": "With HTML",
  "components.year": "Year",
  "components.yesterday": "Yesterday",
  "components.zip": "ZIP Code",
  "components.zipCode": "ZIP Code",

  "config.SMTPSettings": "SMTP Settings",
  "config.append_questionnaires": "Attach questionnaires?",
  "config.checkin": "Perform Check-in",
  "config.confirm": "Participant Confirmation",
  "config.confirmation": "Participant Confirmation",
  "config.customDomain": "Custom Domain",
  "config.custom_fields": "Do you want to use custom fields?",
  "config.event_check": "Associate with an event or campaign?",
  "config.hide_header": "Hide Store Header",
  "config.hide_login": "Hide QIPlus Login Bar",
  "config.hide_menu": "Hide Site Menu",
  "config.integrated": "Integrate with another platform",
  "config.liveqiplus": "Integrate with Live QIPlus",
  "config.managers_mode": "Distribution Mode",
  "config.model_check": "Fill from a template?",
  "config.notification": "Notification",
  "config.notifications": "Notifications",
  "config.participants_score": "Scoring by Participation",
  "config.prevent_dup_ticket":
    "Prevent automation from running more than once on the same ticket",
  "config.prevent_dup_user":
    "Prevent automation from running more than once for the same user",
  "config.questionnaire": "Has Questionnaire",
  "config.raffle": "Conduct Raffles",
  "config.raffleType": "Raffle Type",
  "config.redirect": "Redirect Leads after submission?",
  "config.repeat": "Allow email to be sent more than once to the same contact",
  "config.sales_mode": "Sales Mode",
  "config.score": "Sum scoring by participation",
  "config.sellers_mode": "Distribution Mode",
  "config.stock_check": "Control Inventory",
  "config.use_as_template": "Use as Template",

  "contact.add": "New Contact",
  "contact.clone": "Clone Contact",
  "contact.create": "Create Contact",
  "contact.edit": "Edit Contact",
  "contacts.clone": "Clone Contacts",
  "contacts.edit": "Edit Contacts",

  "contracts.defaultSubject": "QIPlus || Contract",
  "contracts.repeat":
    "Allow the contract to be sent more than once to the same contact",
  "createdAt": "Created on",
  "customers.selectOneCustomer": "Select a customer",
  'date.showGroupFormat': 'dddd, DD [of] MMMM [of] YYYY',
  'date.sortFullDate': 'YYYYMMDDHHmmss',
  'date.sortGroupFormat': 'YYYYMMDD',
  'dates.after': 'After',
  'dates.before': 'Before',
  'dates.between': 'Between',
  'dates.currentMonth': 'Current Month',
  'dates.currentWeek': 'Current Week',
  'dates.currentYear': 'Current Year',
  'dates.custom': 'Custom',
  'dates.dateFormat': 'Format',
  'dates.from': 'From',
  'dates.fromAlt': 'Since',
  'dates.last15Days': 'Last 15 Days',
  'dates.last30Days': 'Last 30 Days',
  'dates.last365Days': 'Last 365 Days',
  'dates.last7Days': 'Last 7 Days',
  'dates.last90Days': 'Last 90 Days',
  'dates.last_28d': 'Last 28 days',
  'dates.last_3d': 'Last 3 days',
  'dates.last_7d': 'Last 7 days',
  'dates.last_90d': 'Last 90 days',
  'dates.last_month': 'Last Month',
  'dates.last_week_sun_sat': 'Last Week',
  'dates.last_year': 'Last Year',
  'dates.modified': 'Modified on',
  'dates.period': 'Period',
  'dates.periodicity': 'Periodicity',
  'dates.this_month': 'This Month',
  'dates.this_week_sun_today': 'This Week',
  'dates.this_year': 'This Year',
  'dates.today': 'Today',
  'dates.until': 'Until',
  'dates.yesterday': 'Yesterday',
  'deals.createFunnelBeforeEdit': 'Create at least one funnel to associate with deals',
  'deals.info': 'Deal Data',
  'deals.newContacTitle': 'Contact',
  'deals.sentTo.lost': 'Deal closed as lost',
  'deals.sentTo.won': 'Deal closed as won',
  'deals.setNewContactData': 'A contact was found in the database with the entered data. Do you want to import it?',
  'domains.DNSInstructions': 'Configure the DNS servers in your domain provider with the following values:',
  'domains.subdomainInstructions': 'To configure a subdomain, use this IP address:',
  'email.content': 'Email Body',
  'email.defaultSubject': 'QIPlus || Email',
  'email.error': 'Error',
  'email.fields.address': 'Address',
  'email.fields.bcc': 'bcc',
  'email.fields.cc': 'cc',
  'email.fields.content': 'Email Body',
  'email.fields.forward': 'Forward to',
  'email.fields.from': 'from',
  'email.fields.fromEmail': 'Sender Email',
  'email.fields.fromName': 'Sender Name',
  'email.fields.host': 'Host',
  'email.fields.html': 'Message',
  'email.fields.password': 'Password',
  'email.fields.port': 'Port',
  'email.fields.provider': 'Email Provider',
  'email.fields.signature': 'Signature',
  'email.fields.subject': 'Subject',
  'email.fields.tls': 'TLS (Security Encryption)',
  'email.fields.to': 'to',
  'email.fields.user': 'User (email)',
  'email.head.bcc': 'bcc',
  'email.head.cc': 'cc',
  'email.head.from': 'from',
  'email.head.subject': 'Subject',
  'email.head.to': 'to',
  'email.pending': 'Pending',
  'email.processing': 'Processing',
  'email.scheduled': 'Scheduled',
  'email.scheduledError': 'Email could not be scheduled',
  'email.scheduledSuccess': 'Email scheduled successfully',
  'email.sending': 'Sending',
  'email.sent': 'Sent',
  'email.sentError': 'Email could not be sent',
  'email.sentSuccess': 'Email sent successfully',
  'email.test': 'Test',
  'email.testSent': 'Test Sent',
  'email.unknown': 'Unknown',
  'emails.attachment': 'Attachment',
  'emails.attachments': 'Attachments',
  'emails.bcc': 'BCC',
  'emails.broadcast': 'Send to entire lead database',
  'emails.cc': 'CC',
  'emails.defaultFooter': 'Default email footer',
  'emails.defaultFooterTip': 'Can be replaced on the email editing screen',
  'emails.defaultSendMethod': 'Default sending method',
  'emails.deleteAllScheduledMail': 'Delete all scheduled emails?',
  'emails.emailTestSuccess': 'Test sent successfully',
  'emails.footer': 'Email footer',
  'emails.footerAddress': 'Address in email footer',
  'emails.footerAddressExample': 'Ex: QIPlus Systems. 30 Adelia de Oliveira St. Residencial Pacaembu, Itupeva - SP. ZIP 13295-000. Brazil',
  'emails.footerAddressTip': 'Enter your complete address, in compliance with the General Data Protection Law (GDPR).',
  'emails.from': 'Sender',
  'emails.fromName': 'Sender Name',
  'emails.markAsSpam': 'Mark as spam',
  'emails.markedAsSpam': 'Emails marked as spam',
  'emails.markedAsSpam.singular': 'Email marked as spam',
  'emails.minDateMessage': 'The sending date and time must be greater than the current date',
  'emails.moveMsg': 'Move Emails',
  'emails.moveMsg.singular': 'Move Email',
  'emails.movedMsg': 'Emails moved successfully',
  'emails.movedMsg.singular': 'Email moved successfully',
  'emails.recipient': 'Recipient',
  'emails.recipients': 'Recipients',
  'emails.repeat': 'Allow the email to be sent more than once to the same contact',
  'emails.scheduleBroadcast': 'Are you sure you want to send to your entire lead database?',
  'emails.scheduleEmail': 'Schedule sending',
  'emails.scheduleNewMail': 'Do you want to schedule the email now?',
  'emails.scheduledSuccess': 'Emails scheduled successfully',
  'emails.scheduled_date': 'Sending Date',
  'emails.selectMailbox': 'Select an Inbox',
  'emails.sendMethod': 'Sending method',
  'emails.sendNewMail': 'Do you want to send the email now?',
  'emails.sendingOptions': 'Sending Options',
  'emails.sentError': 'Emails could not be sent',
  'emails.sentSuccess': 'Emails sent successfully',
  'emails.signinProvider': 'Sign in with your provider',
  'emails.signinProviderAndTyAgain': 'Sign in with your provider and try again',
  'emails.subject': 'Subject',
  'errors.MODULE_LIMIT_REACHED': 'You have reached the limit of items for this QIPlus module. Upgrade your account to increase the limit',
  'errors.MODULE_NOT_AVAILABLE': 'This module is not enabled. Upgrade your account to access it',
  'errors.birthMonth': 'Please select a month',
  'errors.chooseFieldType': 'Select the field type',
  'errors.createChoicesBeforeAddingField': 'Create options for the field',
  'errors.dbErrorMsg': 'An error occurred in the database connection. Please try again',
  'errors.emptyInput': 'Fill in all fields',
  'errors.errorCode': 'Error code',
  'errors.errorFetchingPosts': 'An error occurred while loading the items in this section. Please try again or contact our support if the error persists',
  'errors.failedValidation': 'Fix the errors in the form',
  'errors.fetchErrorMsg': 'An error occurred in the request. Please try again',
  'errors.gender': 'Please select a gender',
  'errors.genericErrorMsg': 'An unexpected error occurred. Please try again or contact our support if the error persists',
  'errors.inexistsErrorMsg': 'The document does not exist in the database',
  'errors.integrationErrorMsg': 'An error occurred in the integration. Please try again or contact our support if the error persists',
  'errors.invalidCNPJ': 'Invalid CNPJ number',
  'errors.invalidCPF': 'Invalid CPF number',
  'errors.invalidEmailAddress': 'The email provided is not valid',
  'errors.invalidForwardAddress': 'The forwarding email is not valid',
  'errors.invalidMonth': 'The month provided is invalid',
  'errors.invalidRecipients': 'Invalid recipient',
  'errors.invalidZipCode': 'Invalid ZIP code',
  'errors.loopDanger': 'This action cannot be performed as it would create an infinite loop.',
  'errors.permissionDenied': 'Your user does not have the necessary permission for this operation',
  'errors.sessionExpired': 'Your authentication session has expired, please log in again',
  'errors.setFieldLabel': 'Enter a label for the field',
  'errors.userHasBeenRemoved': 'The user does not exist or is not active',
  'errors.yearCantBeLowerThanCurrent': 'The year must be greater than or equal to the current year',
  'errors.invalidCustomToken': 'Invalid token or expired',
  'events.participants': 'Event Participants',
  'facebook.facebookRecomendation': 'We recommend that you log in with a business account to avoid conflicts',
  'facebook.logoutAlert': 'If you log out, you will lose all integrations connected to this Facebook account until you connect again to the QIPlus app',
  'facebookcapi.AddPaymentInfo': 'Add payment information',
  'facebookcapi.AddToCart': 'Add to cart',
  'facebookcapi.AddToWishlist': 'Add to wishlist',
  'facebookcapi.CompleteRegistration': 'Complete registration',
  'facebookcapi.Contact': 'Contact',
  'facebookcapi.CustomizeProduct': 'Customize product',
  'facebookcapi.Donate': 'Donate',
  'facebookcapi.FindLocation': 'Find location',
  'facebookcapi.InitiateCheckout': 'Initiate checkout',
  'facebookcapi.Lead': 'Lead',
  'facebookcapi.PageView': 'Page View',
  'facebookcapi.Purchase': 'Purchase',
  'facebookcapi.Schedule': 'Schedule',
  'facebookcapi.Search': 'Search',
  'facebookcapi.StartTrial': 'Start trial period',
  'facebookcapi.SubmitApplication': 'Submit application',
  'facebookcapi.Subscribe': 'Subscribe',
  'facebookcapi.ViewContent': 'View content',
  'facebookcapi.access_token': 'Access Token',
  'facebookcapi.customEvent': 'Custom event',
  'facebookcapi.customEventName': 'Custom event name',
  'facebookcapi.description.AddPaymentInfo': 'The addition of customer payment information during the checkout process. For example, when a person clicks a button to save billing information.',
  'facebookcapi.description.AddToCart': 'The addition of an item to a shopping cart or basket. For example, clicking an Add to Cart button on a website.',
  'facebookcapi.description.AddToWishlist': 'The addition of items to a wishlist. For example, clicking an Add to Wishlist button on a website.',
  'facebookcapi.description.CompleteRegistration': 'The submission of information by a customer in exchange for a service provided by your business. For example, signing up for an email subscription.',
  'facebookcapi.description.Contact': 'A telephone call, SMS, email, chat, or other type of contact between a customer and your business.',
  'facebookcapi.description.CustomizeProduct': 'The customization of products through a configuration tool or other application that your business owns.',
  'facebookcapi.description.Donate': 'The donation of funds to your organization or cause.',
  'facebookcapi.description.FindLocation': 'When a person finds one of your locations on the internet, with the intention of visiting your establishment. For example, searching for a product and finding it in one of your local stores.',
  'facebookcapi.description.InitiateCheckout': 'The start of the checkout process. For example, clicking the Checkout button.',
  'facebookcapi.description.Lead': 'The submission of information by a customer, knowing they may be contacted later by the company. For example, submitting a form or signing up for an evaluation.',
  'facebookcapi.description.PageView': 'This is the standard pixel that tracks page visits. For example, when a person accesses pages on your site',
  'facebookcapi.description.Purchase': 'The completion of a purchase, usually indicated by receiving an order confirmation, purchase or transaction receipt. For example, being directed to a thank you or confirmation page.',
  'facebookcapi.description.Schedule': 'The booking of an appointment to visit one of your locations.',
  'facebookcapi.description.Search': 'A search performed on your website, app, or other property. For example, product or travel searches.',
  'facebookcapi.description.StartTrial': 'The start of a free trial of a product or service you offer. For example, trial subscription.',
  'facebookcapi.description.SubmitApplication': 'The submission of an application for a product, service, or program you offer. For example, credit card, educational program, or job.',
  'facebookcapi.description.Subscribe': 'The start of a paid subscription for a product or service you offer.',
  'facebookcapi.description.ViewContent': 'A visit to a web page that is important to you. For example, a landing page or product page. The View Content option informs if someone visited the URL of a specific web page, without reporting what they did or saw on the page.',
  'facebookcapi.eventContext': 'Event Generation Context',
  'facebookcapi.eventName': 'Event',
  'facebookcapi.eventType': 'Event Type',
  'facebookcapi.externalEvents': 'Events on external site',
  'facebookcapi.pixel_id': 'Pixel ID',
  'facebookcapi.qiplusEvents': 'QIPlus Events',
  'facebookcapi.script': 'Script for the page',
  'facebookcapi.siteAction': 'Site action',
  'facebookcapi.standardEvent': 'Standard event',
  'facebookleads.clickToUnconnect': 'Click to disconnect',
  'facebookleads.connectAsAdmin': 'Log in with your Facebook page administrator user',
  'facebookleads.connectLeadsAdsWith': 'Connect QIPlus with',
  'facebookleads.connectPage': 'Connect with page',
  'facebookleads.connectWith': 'Connect with',
  'facebookleads.connectYourPageToQIPlus': 'Connect your page with the QIPlus Facebook app',
  'facebookleads.connectedAs': 'Connected as ',
  'facebookleads.connectedWithLeadsAds': 'Is connected with QIPlus',
  'facebookleads.connectedWithLeadsAdsInAnotherPost': 'Is connected with QIPlus in another integration',
  'facebookleads.facebookConnectPages': 'Connect to Facebook Pages',
  'facebookleads.loginbtn': 'Login with Facebook',
  'facebookleads.loginbtnAs': 'Log in to Facebook as',
  'facebookleads.logoutbtn': 'Log out of Facebook',
  'facebookleads.noPagesAvailable': 'We could not find pages associated with your user. Make sure you have administrator access to them and try again',
  'facebookleads.page_access_token': 'Access token for your Facebook page',
  'facebookleads.page_id': 'ID of your Facebook page',
  'facebookleads.qiplus_token': 'QIPlus Token',
  'facebookleads.qiplus_verify_token': 'QIPlus verification token',
  'facebookleads.successfullyConnectedPage': 'Congratulations! You have connected your page with the QIPlus Facebook app',
  'facebookleads.successfullyUnconnectedPage': 'You have disconnected your page from the QIPlus Facebook app',
  'facebookleads.unconnectPageWarning': 'Do you want to disconnect your page from the QIPlus Facebook app? Your leads will no longer be sent from your page to the QIPlus platform',
  'facebookleads.userConflictAlert': 'This integration was performed by another Facebook user',
  'facebookleads.userConflictPlaceholder': 'Log in as [%s] to edit the integration',
  'facebookleads.userLoginAlert': 'Log in with the original user to edit the integration',
  'fieldTypes.checkbox': 'Checkbox (multiple choice)',
  'fieldTypes.date_picker': 'Date Picker',
  'fieldTypes.email': 'Email',
  'fieldTypes.number': 'Number',
  'fieldTypes.password': 'Password',
  'fieldTypes.radio': 'Radio buttons (single answer)',
  'fieldTypes.select': 'Selector (single answer)',
  'fieldTypes.text': 'Simple Text',
  'fieldTypes.textarea': 'Text Area',
  'fieldTypes.url': 'URL',
  'format.date': 'DD/MM/YYYY',
  'format.date.full': 'DD/MM/YYYY HH:mm:ss',
  'format.date.full.short.year': 'DD/MM/YY HH:mm:ss',
  'format.date.full.short.year.short.hour': 'DD/MM/YY HH:mm',
  'format.date.short.month': 'MM/YYYY',
  'format.date.short.year': 'YYYY',
  'forms.ID': 'ID',
  'forms.address': 'Address',
  'forms.addressFields': 'Address Fields',
  'forms.addresses': 'Addresses',
  'forms.avatar': 'Avatar',
  'forms.basicFields': 'Basic Fields',
  'forms.birthday': 'Date of Birth',
  'forms.button': 'Button',
  'forms.city': 'City',
  'forms.cnpj': 'CNPJ',
  'forms.comp': 'Complement',
  'forms.companyName': 'Company Name',
  'forms.complementary': 'Complement',
  'forms.contact': 'Contact',
  'forms.contactId': 'Contact',
  'forms.contract': 'Contract',
  'forms.contractNumber': 'Contract Number',
  'forms.contracts': 'Contracts',
  'forms.country': 'Country',
  'forms.countryCode': 'Country Code',
  'forms.cpf': 'CPF',
  'forms.customField': 'Custom Field',
  'forms.customFields': 'Custom Fields',
  'forms.dateAdded': 'Added',
  'forms.dateFields': 'Dates',
  'forms.dateRegistered': 'Registration Date',
  'forms.description': 'Description',
  'forms.displayName': 'Contact Name',
  'forms.document': 'Document',
  'forms.document_number': 'Document Number',
  'forms.document_type': 'Document Type',
  'forms.email': 'Email',
  'forms.facebook': 'Facebook',
  'forms.field': 'Field',
  'forms.fieldBlocks': 'Field Blocks',
  'forms.fieldChoices': 'Field Options',
  'forms.fieldLabel': 'Field Label',
  'forms.fieldName': 'Field Name',
  'forms.fieldPlaceholder': 'Placeholder Text',
  'forms.fieldType': 'Field Type',
  'forms.fieldValue': 'Field Value',
  'forms.fields': 'Fields',
  'forms.firstName': 'First Name',
  'forms.formURL': 'Form URL',
  'forms.fullName': 'Full Name',
  'forms.gender': 'Gender',
  'forms.gender.fem': 'Female',
  'forms.gender.masc': 'Male',
  'forms.genderSelect': 'Select Gender',
  'forms.id': 'ID',
  'forms.instagram': 'Instagram',
  'forms.lastName': 'Last Name',
  'forms.linkedin': 'LinkedIn',
  'forms.manager': 'Account Manager',
  'forms.manager:email': 'Account Manager Email',
  'forms.manager:mobile': 'Account Manager Mobile',
  'forms.manager:name': 'Account Manager Name',
  'forms.manager:phone': 'Account Manager Phone',
  'forms.managerEmail': 'Account Manager Email',
  'forms.managerMobile': 'Account Manager Mobile',
  'forms.managerName': 'Account Manager Name',
  'forms.managerPhone': 'Account Manager Phone',
  'forms.maritalStatus': 'Marital Status',
  'forms.maritalStatus.divorced': 'Divorced',
  'forms.maritalStatus.married': 'Married',
  'forms.maritalStatus.other': 'Other',
  'forms.maritalStatus.single': 'Single',
  'forms.maritalStatus.widowed': 'Widowed',
  'forms.mobile': 'Mobile',
  'forms.mobileWithCC': 'Mobile with Country Code and Area Code',
  'forms.modified': 'Modified',
  'forms.name': 'Name',
  'forms.names': 'Names',
  'forms.neighborhood': 'Neighborhood',
  'forms.newField': 'New Field',
  'forms.nickname': 'Username',
  'forms.num': 'Number',
  'forms.occupation': 'Occupation',
  'forms.optionAdd': 'Add Option',
  'forms.optionLabel': 'Option Label',
  'forms.optionValue': 'Option Value',
  'forms.other': 'Others',
  'forms.password': 'Password',
  'forms.paymentDetails': 'Payment Details',
  'forms.paymentMethod': 'Payment Method',
  'forms.personalize': 'Customize',
  'forms.phone': 'Phone',
  'forms.postalCode': 'ZIP Code',
  'forms.professional': 'Professional',
  'forms.professionals': 'Professionals',
  'forms.profile': 'Profile',
  'forms.promoter': 'Promoter',
  'forms.promoter:email': 'Promoter Email',
  'forms.promoter:mobile': 'Promoter Mobile',
  'forms.promoter:name': 'Promoter Name',
  'forms.promoter:phone': 'Promoter Phone',
  'forms.promoterEmail': 'Promoter Email',
  'forms.promoterMobile': 'Promoter Mobile',
  'forms.promoterName': 'Promoter Name',
  'forms.promoterPhone': 'Promoter Phone',
  'forms.pts': 'Points',
  'forms.pts_gained': 'Points Generated',
  'forms.qiFields': 'QI Plus Fields',
  'forms.qrcode': 'QR Code',
  'forms.qualification': 'Qualification',
  'forms.readonly': 'Read Only',
  'forms.redirect': 'Redirect leads to another destination after form submission?',
  'forms.ref': 'Reference',
  'forms.required': 'Required',
  'forms.requiredFields': 'Required fields',
  'forms.rg': 'RG',
  'forms.saveCustomField': 'Save to database',
  'forms.score': 'Score',
  'forms.score_gained': 'Score Generated',
  'forms.select.birthdayMonth': 'Birth month',
  'forms.seller': 'Seller',
  'forms.seller:email': 'Seller Email',
  'forms.seller:mobile': 'Seller Mobile',
  'forms.seller:name': 'Seller Name',
  'forms.seller:phone': 'Seller Phone',
  'forms.sellerEmail': 'Seller Email',
  'forms.sellerMobile': 'Seller Mobile',
  'forms.sellerName': 'Seller Name',
  'forms.sellerPhone': 'Seller Phone',
  'forms.social': 'Social Networks',
  'forms.socialFields': 'Social Network Fields',
  'forms.socialNewtork': 'Social Network',
  'forms.socialNewtorks': 'Social Networks',
  'forms.stage': 'Stage',
  'forms.state': 'State',
  'forms.street': 'Street',
  'forms.street_number': 'Number',
  'forms.submitConfirmPage': 'Confirmation page',
  'forms.tags': 'Tags',
  'forms.team': 'Team',
  'forms.thankYouMessage': 'Confirmation message',
  'forms.ticket': 'Order',
  'forms.ticketItems': 'Order items',
  'forms.ticketNumber': 'Order number',
  'forms.tickets': 'Orders',
  'forms.title': 'Title',
  'forms.twitter': 'Twitter',
  'forms.type': 'Contact Type',
  'forms.type.corporation': 'Company',
  'forms.type.individual': 'Individual',
  'forms.updatedAt': 'Modified on',
  'forms.url': 'Website',
  'forms.zipcode': 'ZIP Code',
  'funnels.cartMode': 'Shopping Cart',
  'funnels.contactedStage': 'Contacted',
  'funnels.negotiatingStage': 'In Negotiation',
  'funnels.proposedStage': 'Proposal Made',
  'funnels.prospectStage': 'Prospecting',
  'funnels.singleProductMode': 'Single Product',
  'funnels.singleValueMode': 'Fixed Value',
  'funnels.tooltip.actions': 'Build your plan here, defining the automation steps for each stage of your customer journey.',
  'funnels.tooltip.create': 'Start your customer journey.',
  'funnels.tooltip.goal': 'Predefined goals will be shown in the pipeline for sellers/managers.',
  'funnels.tooltip.pipeline': 'Define the sales stages and tasks for your team. \nLost: Lost deals. \nWon: Won deals.',
  'funnels.tooltip.sales_mode': 'The sales mode will affect the deal value and how the seller interacts with the deal card. \nShopping Cart: The seller selects which products the customer purchased, generating a sales ticket priced according to the value of the selected products. \nNegotiated Value: The seller enters the value agreed with the customer. \nFixed Value: All deals are closed with the predefined value.',
  'funnels.tooltip.shotx': 'Choose the available instances',
  'funnels.tooltip.store': 'Optional field. It is recommended to add stores to segment the origin of purchases. Ex: Companies that have more than one unit.',
  'funnels.tooltip.title': 'Write the title of your sales funnel. \n Ex: Lead Capture from Landing Page.',
  'funnels.valuesMode': 'Negotiated Value',
  'goals.checkin': 'Check-in',
  'goals.confirmed': 'Confirmed Participants',
  'goals.daily': 'Daily',
  'goals.final': 'Final',
  'goals.monthly': 'Monthly',
  'goals.participants': 'Participants',
  'goals.registers': 'Registers',
  'goals.views': 'Views',
  'goals.weekly': 'Weekly',
  'googleads.loginAsAdmin': 'Search in Google Ads',
  'hint.searchMailList': 'Search in emails',
  'hint.whatAreYouLookingFor': 'What are You Looking For',
  'icon.stock': 'ti-server',
  'import.updateData': 'Update Existing Records',
  'instructions.acountInvestments': 'Enter the investments made (title, value, periodicity) so we can calculate the return on investment metrics',
  'instructions.investmentQuickField': 'You can use this field to simulate return on investment metrics, even if you have already entered your investments in your settings',
  'instructions.investments': 'Enter the investments made (title and value) to get return on investment metrics',
  'instructions.leaveBlankForModel': 'Leave blank if using only as a template',
  'integrations.addTags': 'Add Tags',
  'integrations.apiKey': 'API Key',
  'integrations.apiUrl': 'API URL',
  'integrations.appID': 'App ID',
  'integrations.associateCustomFields': 'Associate Custom Fields',
  'integrations.bluesoft': 'BlueSoft',
  'integrations.customSMTP': 'Custom SMTP',
  'integrations.default': 'Default',
  'integrations.destinationUrl': 'Destination URL',
  'integrations.eduzz': 'Eduzz',
  'integrations.elementor': 'Elementor',
  'integrations.emailField': 'Email Field',
  'integrations.encriptation': 'Encryption Type',
  'integrations.facebookads.selectCampaign': 'Select a campaign',
  'integrations.fieldAssociated': 'Associated field',
  'integrations.fieldIDOnSource': 'Field ID or name on the platform',
  'integrations.fieldToAssociate': 'Field to associate',
  'integrations.form': 'Forms',
  'integrations.formID': 'Form ID',
  'integrations.g_calendar': 'Google Calendar',
  'integrations.gcalId': 'Calendar ID',
  'integrations.gformsFunction': 'Function for the forms script',
  'integrations.gmail': 'Gmail SMTP',
  'integrations.googleads.connect': 'Connect Google Ads',
  'integrations.googleads.customerDescription': 'Customer description',
  'integrations.googleads.customerId': 'Customer ID',
  'integrations.googleads.customers': 'Google Ads - Customers',
  'integrations.googleads.customersInstructions': 'Enter the Google Ads customers you manage here.',
  'integrations.googleads.getReport': 'Get report',
  'integrations.googleforms': 'Google Forms',
  'integrations.googleformsCollectEmail': 'The form is configured to collect emails',
  'integrations.hotmart': 'Hotmart',
  'integrations.imap': 'IMAP Integration',
  'integrations.imapSettings': 'IMAP Input Settings',
  'integrations.importTags': 'Import Tags',
  'integrations.importTags.instructions': 'Import lead tags from the source platform, when available',
  'integrations.infusionsoft': 'Infusion Soft',
  'integrations.leadlovers': 'Lead Lovers',
  'integrations.mailDomainActive': 'Your Domain is Active',
  'integrations.mailDomainAlreadyExists': 'The domain [%domain] is already configured in an integration.',
  'integrations.mailDomainCheck': 'Check Domain',
  'integrations.mailDomainChecking': 'Checking Domain',
  'integrations.mailDomainInactive': 'Your Domain is not configured, please check',
  'integrations.mailDomainInstruction': 'Access the DNS provider you use to manage <b>[%domain]</b> and add the following DNS records.',
  'integrations.mailDomainMensagem': 'We recommend using the mail subdomain for configuration. Ex: mail.mydomain.com',
  'integrations.mailDomainName': 'Sender Domain',
  'integrations.mailDomainPersonalized': 'Personalized Email',
  'integrations.mandeumzap': 'Send a WhatsApp',
  'integrations.mautic': 'Mautic',
  'integrations.nameGeneric': 'WebHook Name',
  'integrations.notazz': 'Notazz',
  'integrations.optionGeneric': 'WebHook Grouping',
  'integrations.phoneNumber': 'Phone Number',
  'integrations.priorityRecords': 'DNS Receipt Priority',
  'integrations.rdstation': 'RD Station',
  'integrations.recordSendValue': 'Sending Record Type',
  'integrations.recordType': 'Record Type',
  'integrations.recordsValueDNS': 'Record Value',
  'integrations.reports.configure': 'Configure',
  'integrations.reports.noIntegrations': 'No integrations configured',
  'integrations.reports.selectIntegrationAndAccount': 'Select an integration and an account',
  'integrations.reports.selectIntegrationAndCustomer': 'Select an integration and a customer',
  'integrations.selectOneIntegration': 'Select an integration',
  'integrations.sendNameDNS': 'Send name',
  'integrations.sendValue': 'Send Value',
  'integrations.senderEmail': 'Sender Email',
  'integrations.senderName': 'Sender Name',
  'integrations.senderPass': 'Sender Password',
  'integrations.shopify': 'Shopify',
  'integrations.smtp': 'SMTP Integration',
  'integrations.smtpHost': 'SMTP Host',
  'integrations.smtpPort': 'SMTP Port',
  'integrations.smtpSettings': 'SMTP Output Settings',
  'integrations.tags': 'Tags',
  'integrations.tags.instructions': 'Indicate the Tags you want to assign to contacts added through this integration.',
  'integrations.webhookURL': 'Webhook URL',
  'integrations.woocommerce': 'WooCommerce',
  'integrations.yourMailDomain': 'your domain',
  'interactions.module.singular': 'interaction',
  'interactions.module.title': 'Interactions',
  'item.add': 'New item',
  'item.clone': 'Clone item',
  'item.create': 'Create item',
  'item.edit': 'Edit item',
  'items.clone': 'Clone items',
  'items.edit': 'Edit items',
  'items.price': 'Price',
  'items.product': 'Product',
  'items.qty': 'Units',
  'leads.addedToSegmentation': 'Leads added to segmentation',
  'leads.basicInfo': 'Basic Data',
  'leads.corporation': 'Company',
  'leads.deleteConfirmationBody': 'The lead will be permanently deleted',
  'leads.deleteConfirmationTitle': 'Are you sure you want to delete this lead?',
  'leads.dupLead': 'A lead with this data already exists',
  'leads.individual': 'Individual',
  'leads.switchLead': 'Do you want to edit the lead?',
  'leads.type': 'Contact Type',
  'logs.noLogsYet': 'No logs yet',
  'logs.viewAllLogFields': 'View all',
  'logs.viewGroupedLogFields': 'View summary',
  'mail.errors.emailTestFailed': 'An error occurred while sending the test email',
  'mail.errors.emptyFromName': 'Fill in the sender name',
  'mail.errors.emptySendMethod': 'Choose the sending method',
  'mail.errors.emptySubject': 'Fill in the subject field',
  'mail.errors.errorSendingEmail': 'An error occurred while sending the email',
  'mail.errors.errorSendingEmails': 'An error occurred while sending the email(s)',
  'mail.errors.invalidBody': 'The email content cannot be null',
  'mail.errors.invalidFromName': 'The sender name cannot be null',
  'mail.errors.invalidRecipients': 'Invalid recipient',
  'mailboxes.IMAP': 'IMAP - Own Domain',
  'mailboxes.authenticateInGmail': 'Authenticate your Gmail account',
  'mailboxes.availableMailboxes': 'You can use any of these available addresses:',
  'mailboxes.confirmCloudMailboxCreation': 'A new inbox will be created and the sending email cannot be changed later for this Inbox',
  'mailboxes.confirmCreation': 'Confirm the creation of your Inbox',
  'mailboxes.draft': 'Drafts',
  'mailboxes.inbox': 'Inbox',
  'mailboxes.labels.CATEGORY_FORUMS': 'Forums',
  'mailboxes.labels.CATEGORY_PERSONAL': 'Personal',
  'mailboxes.labels.CATEGORY_PROMOTIONS': 'Promotions',
  'mailboxes.labels.CATEGORY_SOCIAL': 'Social',
  'mailboxes.labels.CATEGORY_UPDATES': 'Updates',
  'mailboxes.labels.CHAT': 'Chat',
  'mailboxes.labels.DRAFT': 'Draft',
  'mailboxes.labels.IMPORTANT': 'Important',
  'mailboxes.labels.INBOX': 'Inbox',
  'mailboxes.labels.READ': 'Read',
  'mailboxes.labels.SENT': 'Sent',
  'mailboxes.labels.SPAM': 'Spam',
  'mailboxes.labels.STARRED': 'Starred',
  'mailboxes.labels.TRASH': 'Trash',
  'mailboxes.labels.UNREAD': 'Unread',
  'mailboxes.labels.UNSTARRED': 'Unstarred',
  'mailboxes.labels.trash': 'Trash',
  'mailboxes.sent': 'Sent',
  'mailboxes.spam': 'Spam',
  'mailboxes.trash': 'Trash',
  'mailboxes.unavailableEmailAddress': 'The email address is not available',
  'managers.roulette': 'Managers Roulette',
  'menu.account': 'My Account',
  'menu.app': 'App',
  'menu.campaigns': 'Campaigns',
  'menu.contact': 'Contact',
  'menu.contacts': 'Contacts',
  'menu.dashboard': 'Dashboard',
  'menu.deals': 'Deals',
  'menu.hunters': 'QI Hunter',
  'menu.import': 'Import',
  'menu.managers': 'Managers',
  'menu.model': 'Model',
  'menu.models': 'Models',
  'menu.myProfile': 'My Profile',
  'menu.reports': 'Reports',
  'menu.segmentationsGroup': 'Segmentations',
  'menu.sellers': 'Sellers',
  'menu.tags': 'Tags',
  'menu.teams': 'Teams',
  'menu.tools': 'Tools',
  'menu.tooltip.dashboard': 'See your business development here',
  'menu.users': 'Users',
  'message.shotx.none': 'No ShotX configured',
  'messagesBroadcast.changed': 'Mass message updated successfully',
  'messagesBroadcast.error': 'Something happened when updating mass message',
  'messagesBroadcast.module.description': 'Sending mass messages',
  'messagesBroadcast.module.title': 'Mass Messages',
  'messagesBroadcast.schedule.error': 'Something happened when scheduling mass message sending',
  'messagesBroadcast.schedule.success': 'Mass message sending scheduled successfully',
  'messagesBroadcast.save.success': 'Broadcast saved successfully',
  'messagesBroadcast.send.error': 'Something happened when sending mass message',
  'modal.banner.buttonPrimary': 'Get ShotX',
  'modal.banner.buttonSecondary': 'Cancel',
  'modal.banner.checkedNoShowAgain': 'Do Not Show Again',
  'modal.banner.title': 'Getting to Know ShotX',
  'modal.cancel': 'Cancel',
  'modal.close': 'Close',
  'modal.export': 'Export',
  'modal.export.completed': 'Export all columns',
  'modal.export.csv': 'Export to CSV',
  'modal.export.resumed': 'Export Main Columns',
  'modal.export.xlsx': 'Export to XLSX',
  'modal.title': 'Export records',
  'module.api': 'Api',
  'module.backend': 'Backend',
  'module.drafts': 'Drafts',
  'module.frontend': 'Frontend',
  'module.inbox': 'Inbox',
  'module.issue': 'Issue',
  'module.sent': 'Sent',
  'module.spam': 'Spam',
  'module.trash': 'Trash',
  'modules.app.name': 'qiplus',
  'modules.quick_messages': 'Quick Messages',
  'modules.quick_messages.added': 'Quick Message Created',
  'modules.quick_messages.changed': 'Quick Message Updated',
  'modules.quick_messages.removed': 'Quick Message Removed',
  'modules.quick_messages.short': 'Quick Message',
  'modules.quick_messages.singular': 'Quick Message',
  'nf.LC116': 'LC 116 Service List Item',
  'nf.aliquotas': 'Tax Rates',
  'nf.apiError': 'An error occurred while generating the Invoice',
  'nf.cnae': 'CNAE',
  'nf.competence': 'Billing Period',
  'nf.customer': 'Customer',
  'nf.goal': 'Purpose',
  'nf.ie': 'State Registration',
  'nf.im': 'Municipal Registration',
  'nf.iss_check': 'ISS withheld at source',
  'nf.issue_date': 'Issue Date',
  'nf.loadingMessage': 'Generating Invoice',
  'nf.nature_operation': '',
  'nf.operation_type': 'Operation Type',
  'nf.product': 'Product',
  'nf.referenced': 'Referenced invoice',
  'nf.service': 'Service',
  'nf.service_code': 'Municipality service code',
  'nf.service_description': 'Municipality service description',
  'nf.successMessage': 'Invoice generated successfully!',
  'nf.taxtype': 'Person Type',
  'nf.title': 'Invoice',
  'nf.type': 'Invoice Type',
  'nf.type_product': 'Products',
  'nf.type_service': 'Services',
  'notification.check': 'Notification',
  'notification.interval': '',
  'notification.qty': '',
  'notifications.check': 'Notifications',
  'notifications.emptyMsg': 'No alerts for now',
  'pagarme.openDashboard': 'View in Pagarme Dashboard',
  'pagarme.openManageUrl': 'Edit with Pagarme',
  'pagarme.openPendingUpdate': 'View update in Pagarme',
  'participants.checkin': 'Check-in confirmed',
  'participants.checkinTime': 'Check-in at',
  'participants.confirm': 'Confirm Participant',
  'participants.confirmed': 'Participant confirmed',
  'participants.confirmedTime': 'Confirmed at',
  'participants.disconfirm': 'Unconfirm Participant',
  'participants.disconfirmed': 'Participant unconfirmed',
  'participants.undoCheckin': 'Check-in canceled',
  'payment.accountDoesNotExist': 'The account does not exist or is not active',
  'payment.accountHasPendingUpdates': 'This account has pending upgrades',
  'payment.accountIsNotActive': 'Oops, it seems your account is not active',
  'payment.accountType': 'Account type',
  'payment.activeSubscriptionAlert': 'There is already an active subscription for this account. Cancel the subscription before generating a new one',
  'payment.agencia': 'Agency',
  'payment.agencia_dv': 'Agency Verification Digit',
  'payment.bankAccount': 'Bank Account',
  'payment.bankCode': 'Bank Code',
  'payment.billing': 'Billing',
  'payment.billingAccountNotFound': 'You do not have an active account in QI Plus yet. Create your account now or contact us to learn more about QIPlus.',
  'payment.boleto': 'Bank Slip',
  'payment.cancelPendingUpdates': 'Cancel Pending Subscriptions',
  'payment.cancelSubscription': 'Cancel Subscription',
  'payment.cancelSubscriptionError': 'An error occurred while canceling the subscription',
  'payment.cardCustomerName': 'NAME ON CARD',
  'payment.cash': 'Cash Payment',
  'payment.checkout': 'Checkout',
  'payment.checkoutSuccess': 'Checkout completed successfully',
  'payment.commission': 'Commission',
  'payment.commissions': 'Commissions',
  'payment.confirmCancelSubscription': 'Are you sure you want to cancel the subscription? This action cannot be reversed later',
  'payment.confirmCheckout': 'Proceed with checkout anyway',
  'payment.confirmPayment': 'Confirm Payment',
  'payment.conta': 'Account number',
  'payment.conta_dv': 'Account Verification Digit',
  'payment.credit_card': 'Credit Card',
  'payment.delivery': 'Delivery',
  'payment.delivery_fee': 'Delivery Fee',
  'payment.discount': 'Discount',
  'payment.discounts': 'Discounts',
  'payment.document_number': 'Document (CPF or CNPJ)',
  'payment.extras': 'Extras',
  'payment.gateway': 'Payment Method',
  'payment.gateway_type': 'Billing Method',
  'payment.generateBoleto': 'Generate Payment Slip',
  'payment.implementation': 'Implementation',
  'payment.implementationAlreadyCharged': 'Implementation already charged in the first subscription',
  'payment.implementationCost': 'Implementation cost',
  'payment.installment': 'Installment',
  'payment.installments': 'Installments',
  'payment.legal_name': 'Account Holder',
  'payment.maxInstallments': 'Up to',
  'payment.mercadoPagoCredentials': 'Mercado Pago Credentials',
  'payment.noMonthlySubscription': 'The plan does not have a monthly subscription',
  'payment.oneTimePayment': 'in cash',
  'payment.other_discounts': 'Other discounts',
  'payment.other_fees': 'Other fees',
  'payment.other_taxes': 'Other taxes',
  'payment.paymentLink': 'Payment link',
  'payment.planDetails': 'Plan Details',
  'payment.planValue': 'Plan Value',
  'payment.pleaseCheckYourEmail': 'Please check your email to access the payment link or change the payment method',
  'payment.price': 'Price',
  'payment.prices': 'Prices',
  'payment.printYourBoleto': 'Print your bank slip to make the payment',
  'payment.pts': 'QI-Loyalty Points',
  'payment.recipient': 'Recipient',
  'payment.recurrency': 'Recurrence',
  'payment.secureCheckout': '100% secure purchase',
  'payment.startingAt': 'Starting at',
  'payment.subscription': 'Subscription',
  'payment.taxes': 'Taxes',
  'payment.ticketValue': 'Order Total',
  'payment.total': 'Total',
  'payment.totals': 'Totals',
  'payment.updatesWillBeReadyOnPaymentConfirm': 'The account update will be automatically activated after payment confirmation',
  'payment.upgrade': 'Upgrade',
  'payment.validThru': 'valid until',
  'payment.value': 'Value',
  'payment.values': 'Values',
  'payment.viaCreditCard': 'on card',
  'payment.yourAccountWillBeReadyOnPaymentConfirm': 'Your account will be automatically activated after payment confirmation',
  'payment.yourAccountWillBeReadySoon': 'Your account will be active in a few moments',
  'pipeline.addOneStage': 'Add a Stage',
  'pipeline.addStage': 'Add Stage',
  'pipeline.createSegmentation': 'Create Segmentation',
  'pipeline.dstStage': 'Destination Stage',
  'pipeline.options': 'Pipeline Options',
  'pipeline.orderByMensages': 'Number of unread messages',
  'pipeline.orderByModified': 'Modification Date',
  'pipeline.orderByScoring': 'Scoring',
  'pipeline.orderby': 'Order by',
  'pipeline.removeOneStage': 'Remove a Stage',
  'pipeline.removeStage': 'Remove Stage',
  'pipeline.stage': 'Stage',
  'pipeline.stages': 'Stages',
  'placeholders.MODULE_LIMIT_AVAILABLE': 'The remaining limit of [%s] in your account is [%n]',
  'placeholders.MODULE_LIMIT_REACHED': 'You have reached the [%s] limit of your account. Upgrade to increase the limit',
  'placeholders.SMTP_SETTINGS_CONFIRM_EMAAIL': '<p><img alt=\'\' src=\'https://qiplus.com.br/ckfiles/i9TtRqSINnaVr0f7x4uDBtk6SmY2/images/template-logo-600x200.jpg\' style=\'height:200px; width:600px\' /></p><p>&nbsp;</p><p>Congratulations, [%s]!</p><p>&nbsp;</p><p>Your SMTP settings have been correctly configured!</p><p>&nbsp;</p><p><strong>QIPlus Team</strong></p>',
  'placeholders.confirmOwnerUpdate': 'Do you want to update account to [%s]?',
  'placeholders.confirmRemoval': 'Do you want to delete [%s]?',
  'placeholders.confirmUpdate': 'Do you want to update [%s]?',
  'placeholders.create': 'Create [%s]',
  'placeholders.customMailNotValid': 'The sender does not belong to the custom domain',
  'placeholders.email': 'Email',
  'placeholders.errorFetchingPosts': 'An error occurred while loading [%s]. Please try again',
  'placeholders.errorTrashingPosts': 'An error occurred while deleting [%s]. Please try again',
  'placeholders.errorUpdatingPosts': 'An error occurred while updating [%s]. Please try again',
  'placeholders.events.checkin': 'Check-in of Participants of [%s]',
  'placeholders.events.participants': 'Participants of [%s]',
  'placeholders.inexistsErrorMsg': '[%s] does not exist in the database',
  'placeholders.invalidField': 'The field [%s] is invalid',
  'placeholders.logoutAlert': 'You need to log out of your current account [%s] to connect a new account',
  'placeholders.maxEmailsError': 'The maximum number of emails for the field [%s] is [%max]',
  'placeholders.maxValueError': 'The maximum value for the field [%s] is [%max]',
  'placeholders.minEmailsError': 'The minimum number of emails for the field [%s] is [%min]',
  'placeholders.minRowsError': 'Select at least [%min] [%s]',
  'placeholders.minValueError': 'The minimum value for the field [%s] is [%min]',
  'placeholders.noGoalsFound': 'You have not yet loaded the goal information in [%s].',
  'placeholders.noInvestmentsFound': 'You have not yet loaded the investment information in [%s].',
  'placeholders.noPostsFound': 'There are no [%s] in your account',
  'placeholders.noResultsFound': 'There are no [%s] that match your search',
  'placeholders.noUsersFound': 'There are no [%s] in your account',
  'placeholders.password': 'Password',
  'placeholders.requiredField': 'The field [%s] is required',
  'placeholders.select': 'Select...',
  'placeholders.selectAnItem': 'Select [%s]',
  'placeholders.thankYouMessageLabel': 'Thank you!',
  'placeholders.updateCampaignSettings': 'Complete the campaign settings to access the full QIPlus reports.',
  'placeholders.updateSettings': 'Complete the [%s] settings to access the full QIPlus reports.',
  'policy.cookiesAcceptanceEntries': 'View Cookie Policy acceptance records',
  'policy.cookiesPolicy': 'Cookie Policy',
  'preferences.currency': 'USD',
  'preferences.currencySymbol': '$',
  'preferences.locale': 'en-US',
  'privacy.privacyAcceptanceEntries': 'View Privacy Policy acceptance records',
  'privacy.privacyPolicy': 'Privacy Policy',
  'privacy.privacyScript': 'Script for Privacy Policy acceptance',
  'privacy.privacyText': 'Privacy Policy text',
  'privacy.privacyType': 'Privacy Policy content',
  'privacy.privacyUrl': 'Privacy Policy URL',
  'privacy.termsAcceptanceEntries': 'View Terms of Use acceptance records',
  'privacy.termsOfUse': 'Terms of Use',
  'privacy.termsScript': 'Script for Terms of Use acceptance',
  'privacy.termsText': 'Terms of Use text',
  'privacy.termsType': 'Terms of Use content',
  'privacy.termsUrl': 'Terms of Use URL',
  'qiusers.corporation': 'Company',
  'qiusers.deleteConfirmationBody': 'The user will be permanently deleted',
  'qiusers.deleteConfirmationTitle': 'Are you sure you want to delete this user?',
  'qiusers.dupLead': 'A user with this data already exists',
  'qiusers.individual': 'Individual',
  'qiusers.switchLead': 'Do you want to edit the user?',
  'qiusers.type': 'User Type',
  'questionnaires.answer': 'Answer',
  'questionnaires.answers': 'Answers',
  'questionnaires.question': 'Question',
  'questionnaires.questions': 'Questions',
  'questionnaires.score': 'Score',
  'questionnaires.scores': 'Scores',
  'quickMessages.add': 'New Message',
  'quickMessages.cancel': 'Cancel',
  'quickMessages.create': 'Create Message',
  'quickMessages.edit': 'Edit Message',
  'quickMessages.enabled': 'Enabled',
  'quickMessages.enabledOnAutomation': 'Automation',
  'quickMessages.enabledOnQuickMessage': 'Quick messages',
  'quickMessages.message': 'Message',
  'quickMessages.module.singular': 'Quick Message',
  'quickMessages.module.title': 'Quick Messages',
  'quickMessages.module.tooltip': 'Quick Messages for Conversations',
  'quickMessages.no': 'No',
  'quickMessages.releaseOn': 'Available on',
  'quickMessages.tooltipAutomation': 'Will be available for selection in ShotX automations',
  'quickMessages.tooltipShotx': 'Will be available to send messages with one click to a deal through ShotX',
  'quickMessages.vars': 'Variables',
  'quickMessages.vars.clientName': 'Client Name',
  'quickMessages.vars.vendorName': 'Seller Name',
  'quickMessages.yes': 'Yes',
  'redirect.customUrl': 'Custom URL',
  'redirect.qiplusPage': 'QIPlus Page',
  'repeater.removeOneItem': 'Remove an Item',
  'reports.accepted': 'Accepted',
  'reports.accepted.singular': 'Accepted',
  'reports.ads': 'from Ads',
  'reports.ageRangeError': 'The age range is invalid, the start must be less than the end',
  'reports.clickLink': 'Click on the Link',
  'reports.clicked': 'Clicked',
  'reports.clicked.singular': 'Clicked',
  'reports.clicks': 'Clicks',
  'reports.complained': 'Complaints',
  'reports.complained.singular': 'Complaint',
  'reports.conversionsValue': 'Conversions',
  'reports.cost': 'Cost',
  'reports.costMicros': 'Costs',
  'reports.costs': 'Costs',
  'reports.cpc': 'Cost per Click',
  'reports.ctr': 'CTR',
  'reports.delivered': 'Delivered',
  'reports.delivered.singular': 'Delivered',
  'reports.engagement': 'Engagement',
  'reports.error.singular': 'Not delivered',
  'reports.errorMsg': 'Selecione uma página ou redefina o intervalo de datas',
  'reports.failed': 'Not delivered',
  'reports.failed.singular': 'Not delivered',
  'reports.followers': 'Followers',
  'reports.frequency': 'Frequency',
  'reports.hardBounces': 'Hard Bounces',
  'reports.impressions': 'Impressions',
  'reports.interactions': 'Interactions',
  'reports.interactionsTax': 'Interaction rate',
  'reports.last_28d': 'Last 28 days',
  'reports.last_3d': 'Last 3 days',
  'reports.last_7d': 'Last 7 days',
  'reports.last_90d': 'Last 90 days',
  'reports.last_month': 'Last Month',
  'reports.last_week_sun_sat': 'Last Week',
  'reports.last_year': 'Last Year',
  'reports.metrics': 'Metrics',
  'reports.noData': 'No data to display',
  'reports.opened': 'Opened',
  'reports.opened.singular': 'Opened',
  'reports.organic': 'from Organic',
  'reports.page_fan_adds_unique': 'Followers',
  'reports.page_fan_adds_unique_Description': 'The number of new people who liked your Page.',
  'reports.page_impressions': 'Page Impressions',
  'reports.page_impressions_Description': 'The number of times any content from your Page or about it appeared on a person\'s screen. This includes posts, stories, ads, and other content or information from your Page.',
  'reports.page_posts_impressions': 'Page Posts',
  'reports.page_posts_impressions_Description': 'The number of times your Page posts appeared on a person\'s screen. Posts include status updates, photos, links, videos, and more.',
  'reports.page_views_total': 'Page Views',
  'reports.page_views_total_Description': 'The number of times a Page profile was viewed by people who were logged in and logged out.',
  'reports.preset_date': 'Select Period',
  'reports.range': 'Reach',
  'reports.rangeDescription': 'This metric counts the reach of organic or paid distribution of your content on Facebook, including posts, stories, and ads. It also includes reach from other sources, such as tags, check-ins, and visits to your Page or profile. This number also includes the reach of posts and stories that were boosted. Reach is only calculated once if it occurs through both organic and paid distribution. This metric is estimated.',
  'reports.reach': 'Reach',
  'reports.recipient': 'Recipient',
  'reports.recipients': 'Recipients',
  'reports.rejected': 'Rejected',
  'reports.rejected.singular': 'Rejected',
  'reports.replied.singular': 'Replied',
  'reports.selectPage': 'Select Page',
  'reports.sent': 'Sent',
  'reports.sent.singular': 'Sent',
  'reports.shots': 'Shots',
  'reports.softBounces': 'Soft Bounces',
  'reports.spend': 'Cost',
  'reports.this_month': 'This Month',
  'reports.this_week_sun_today': 'This Week',
  'reports.this_year': 'This Year',
  'reports.today': 'Today',
  'reports.total': 'Total',
  'reports.uniqueRecipients': 'Unique Recipients',
  'reports.unsubscribed': 'Unsubscribed',
  'reports.unsubscribed.singular': 'Unsubscribed',
  'reports.viewed': 'Viewed',
  'reports.viewed.singular': 'Viewed',
  'reports.yesterday': 'Yesterday',
  'roles.accountOwner': 'Account Owner',
  'roles.admin': 'System Administrator',
  'roles.admins': 'System Administrators',
  'roles.affiliate': 'Affiliate',
  'roles.affiliates': 'Affiliates',
  'roles.lead': 'Lead',
  'roles.leads': 'Leads',
  'roles.manager': 'Account Manager',
  'roles.managers': 'Account Managers',
  'roles.operator': 'Operator',
  'roles.operators': 'Operators',
  'roles.owner': 'Administrator',
  'roles.owners': 'Administrators',
  'roles.promoter': 'Promoter',
  'roles.promoters': 'Promoters',
  'roles.role': 'Role',
  'roles.roles': 'Roles',
  'roles.seller': 'Seller',
  'roles.sellers': 'Sellers',
  'roles.webmaster': 'Webmaster',
  'roles.webmasters': 'Webmasters',
  'roulette.equal': 'Equal Distribution',
  'roulette.random': 'Random Distribution',
  'segmentations.add.leads': 'Add Leads to segmentation?',
  'segmentations.add.name': 'New Segmentation Name',
  'segmentations.add.new': 'Create new Segmentation',
  'segmentations.add.new.text': 'You are about to create a new segmentation with the listed leads, do you want to continue?',
  'segmentations.dinamic': 'Dynamic Segmentation',
  'segmentations.dinamicAlert': 'This Segmentation was designated as Dynamic when it was created. It is not possible to change the Segmentation type after its creation',
  'segmentations.dinamicConfigAlert': 'When saving the Segmentation for the first time, it will be configured as Static or Dynamic and it will not be possible to change it later',
  'segmentations.dinamicLeads': 'Leads in Segmentation',
  'segmentations.dinamicLeadsMatches': 'Leads that match the Segmentation criteria',
  'segmentations.dinamicType': 'Dynamic',
  'segmentations.static': 'Static Segmentation',
  'segmentations.staticAlert': 'This Segmentation was designated as Static when it was created. It is not possible to change the Segmentation type after its creation',
  'segmentations.staticType': 'Static',
  'segmentations.title': 'Segmentations',
  'sellers.roulette': 'Sellers Roulette',
  'shortcodes.ID': 'Number',
  'shortcodes.data:contractNumber': 'Contract Number',
  'shortcodes.data:total': 'Order Total',
  'shortcodes.date': 'Creation Date',
  'shortcodes.items': 'Order items',
  'shortcodes.modified': 'Modification Date',
  'shortcodes.payment:gateway': 'Payment Method',
  'shortcodes.sentDate': 'Day of the Month of Sending',
  'shortcodes.sentDay': 'Day of the week of sending',
  'shortcodes.sentFullDate': 'Sending Date',
  'shortcodes.sentMonth': 'Month of Sending',
  'shortcodes.sentPlusDate': 'Date from sending',
  'shortcodes.sentYear': 'Year of Sending',
  'shortcodes.tips.sentDate': 'Ex: 31',
  'shortcodes.tips.sentDay': 'Ex: Monday',
  'shortcodes.tips.sentFullDate': 'MM/DD/YYYY',
  'shortcodes.tips.sentMonth': 'Ex: March',
  'shortcodes.tips.sentYear': 'Ex: 2020',
  'shortcodes.tips.type': 'Individual or Company',
  'shortcodes.type': 'Account Type',
  "shotx.add.apiKeys": "Add your ShotFlow keys here",
  'shotx.apiKeys.saveApiKeysSucessfully': 'Keys saved successfully',
  'shotx.broadcast.cancel': 'Cancel',
  'shotx.broadcast.cancel.dialog.description': 'Are you sure you want to cancel sending the mass message?',
  'shotx.broadcast.cancel.dialog.title': 'Mass Message Cancellation',
  'shotx.broadcast.edit': 'Editing ',
  'shotx.broadcast.failed': 'Not Delivered',
  'shotx.broadcast.indicators': 'Indicators',
  'shotx.broadcast.noBroadcasts': 'There are no mass messages',
  'shotx.broadcast.selectPlataform': 'Select the platform to send messages',
  "shotx.broadcast.selectContent": "Choose the type of content to be sent",
  "shotx.broadcast.selectQuickMessage": "Select the message to be sent to contacts",
  "shotx.broadcast.selectQuickMessageView": "Preview of the message that will be sent to contacts",
  "shotx.broadcast.selectSniper": "Select the sniper that will be assigned to the contacts",
  "shotx.broadcast.completAllCamps": "Complete all required fields to continue",
  "shotx.broadcast.schedulAlert": "🔔 Reminder: Scheduled bulk messages can be edited up to 5 minutes before they are sent.",
  "shotx.broadcast.selectSegmentations": "Select segmentations to filter contacts",
  "shotx.broadcast.selectLeads": "Select the contacts who will receive the message",
  "shotx.broadcast.noSegmentations": "No segmentations available",//
  "shotx.broadcast.allCampsOk": "All set! Click the button below to finish",//
  "shotx.broadcast.checkInputs": "Make sure you have selected an instance, contacts and a message/sniper",//
  "shotx.broadcast.requiredSchedule": "It is also necessary to set a scheduling date.",//
  'shotx.broadcast.instagram.delivered': 'There was no data for the instagram type',
  'shotx.broadcast.instanceSelect': 'Select an instance',
  'shotx.broadcast.instanceSelectLabel': 'Select or search for an instance',
  'shotx.broadcast.leadsQtd': 'Number of Contacts',
  'shotx.broadcast.leadsSelect': 'Select leads',
  'shotx.broadcast.leadsSelectLabel': 'Select or search for leads',
  'shotx.broadcast.messageContent': 'Message Content',
  'shotx.broadcast.messagePreview': 'Message Preview',
  'shotx.broadcast.messageSelect': 'Select a message',
  'shotx.broadcast.messageSelectLabel': 'Select or search for a message',
  'shotx.broadcast.messageTo': 'To',
  'shotx.broadcast.multipleSelection': 'Multiple Selection',
  'shotx.broadcast.noLeads': 'No leads were found for this instance',
  'shotx.broadcast.replieds': 'Replied',
  'shotx.broadcast.rules': 'Sending Rules',
  'shotx.broadcast.schedule': 'Schedule Sending',
  'shotx.broadcast.schedule.cancel': 'Cancel Sending',
  'shotx.broadcast.scheduleDate': 'Sending Date and Time',
  'shotx.broadcast.segmentationsSelect': 'Select segmentations',
  'shotx.broadcast.segmentationsSelectLabel': 'Select or search for segmentations',
  'shotx.broadcast.segmetationsQtd': 'Number of Segmentations',
  'shotx.broadcast.selectAll': 'Select all',
  'shotx.broadcast.selectedInstances': 'Selected Instances',
  'shotx.broadcast.selectInstance': 'Select an instance to load available Leads',
  'shotx.broadcast.selectInstances': 'Select one or more instances to load available Leads',
  'shotx.broadcast.selectNone': 'Clear selection',
  'shotx.broadcast.sendLater': 'Schedule Sending',
  'shotx.broadcast.sendNow': 'Send Now',
  'shotx.broadcast.sendTypeSelect': 'Select sending method',
  'shotx.broadcast.sendTypeSniper': 'Trigger ShotFlow',
  'shotx.broadcast.sendTypeText': 'Send Message',
  'shotx.broadcast.sendingStatus': 'Sending Status',
  'shotx.broadcast.sent': 'Sent',
  'shotx.broadcast.sniperSelect': 'Select a ShotFlow',
  'shotx.broadcast.sniperSelectLabel': 'Select or search for a ShotFlow',
  'shotx.broadcast.status.sent': 'Sent',
  'shotx.broadcast.tagsFilter': 'Filter by Tags',
  'shotx.broadcast.title': 'Mass Message Sending',
  'shotx.broadcast.title.analytcs': '[%type] [%date_schedule] [%platform]',
  'shotx.broadcast.title.editing': 'Editing [%type] [%date_schedule]',
  'shotx.broadcast.total': 'Total',
  'shotx.broadcast.interval': 'Message Interval',
  'shotx.broadcast.intervalQty': 'Quantity',
  'shotx.broadcast.intervalUnit': 'Unit',
  'shotx.broadcast.validation.noInstancesSelected': 'Select at least one instance',
  'shotx.broadcast.validation.noInstanceSelected': 'Select an instance',
  'shotx.broadcast.validation.invalidInstances': 'Some selected instances are inactive or unavailable',
  'shotx.broadcast.validation.invalidInstance': 'The selected instance is inactive or unavailable',
  'shotx.broadcast.validation.noLeadsSelected': 'Select at least one lead',
  'shotx.broadcast.validation.noMessageSelected': 'Select a message',
  'shotx.broadcast.warning.inactiveInstances': 'Inactive instances detected',
  'shotx.broadcast.warning.noValidLeads': 'No valid leads found in selected instances',
  'shotx.broadcast.error.loadingLeads': 'Error loading leads from instances',
  'shotx.chat.audioNotAvailable': 'Audio unavailable',
  'shotx.chat.discard': 'Discard',
  'shotx.chat.fileNotAvailable': 'File unavailable',
  'shotx.chat.imageNotAvailable': 'Image unavailable',
  'shotx.chat.send': 'Send',
  'shotx.chat.sendMedia': 'Send media',
  'shotx.chat.videoNotAvailable': 'Video unavailable',
  'shotx.connectedNumber': 'Connected Number',
  'shotx.instagram.instance.create.info': ' It is not possible to connect the same Instagram account to more than one instance',
  'shotx.instagram.instance.title.placeholder': 'Instagram Instance Name',
  'shotx.instagram.noContact': 'Link an interaction to the Lead to send a message.',
  'shotx.instagram.noIntegrations': 'No integration configured',
  'shotx.instagram.notReady': 'Instagram is not ready for use yet',
  'shotx.instagram.onRefused': 'Connection refused',
  'shotx.instagram.selectLead': 'Select a lead',
  'shotx.instance.connect': 'Connect',
  'shotx.instance.connected': 'Connected',
  'shotx.instance.connectedSuccessfully': 'Connected successfully',
  'shotx.instance.connecting': 'Connecting',
  'shotx.instance.create': 'Create instance',
  'shotx.instance.disconnect': 'Disconnect',
  'shotx.instance.disconnected': 'Disconnected',
  'shotx.instance.disconnectedInfo': 'Disconnected. Please connect QIPlus to your [%platform] and try again.',
  'shotx.instance.disconnectedSuccessfully': 'Disconnected successfully',
  'shotx.instance.disconnecting': 'Disconnecting',
  'shotx.instance.edit': 'Editing [%instance]',
  'shotx.instance.interactions': 'Interactions',
  'shotx.instance.interactions.notVinculated': 'No linked lead',
  'shotx.instance.interactions.title': 'Interactions of [%instance]',
  'shotx.instance.interactions.unvinculate': 'Unlink Lead',
  'shotx.instance.interactions.vinculate': 'Link Lead',
  'shotx.instance.loading': 'Loading',
  'shotx.instance.noMessages': 'No messages',
  'shotx.instance.qrcode': 'Scan the QR code with your WhatsApp application.',
  'shotx.instance.refused': 'The connection time has expired, please try again',
  'shotx.instance.saveChangesSuccessfully': 'Changes saved successfully',
  'shotx.instance.willBeDeleted': 'When deleting the instance, all data and messages will be moved to the trash. Do you want to continue? This operation cannot be undone',
  'shotx.module.title': 'ShotX',
  'shotx.module.tooltip': 'Chats integrated with ShotX',
  'shotx.plataform.config': 'Settings',
  'shotx.plataform.title': 'Platforms',
  'shotx.plataform.whatsapp.title': 'WhatsApp',
  'shotx.snipers.apiKeyToken': 'API Token',
  'shotx.snipers.form.apiKey': 'Add your API Token',
  'shotx.snipers.form.keyword': 'Keyword',
  'shotx.snipers.form.keywordFinish': 'Word to Finish ShotFlow',
  'shotx.snipers.form.keywordFinishPlaceholder': 'Word to End Conversation with ShotFlow',
  'shotx.snipers.form.selectTrigger': 'Select trigger',
  'shotx.snipers.form.selectTypeTrigger': 'Select trigger type',
  'shotx.snipers.form.trigger': 'Trigger',
  'shotx.snipers.form.typeTrigger': 'Trigger Type',
  'shotx.snipers.form.unknownMessage': 'Unknown Message',
  'shotx.snipers.form.unknownMessagePlaceholder': 'Phrase when receiving an unknown option',
  'shotx.snipers.form.withoutApiKey': 'API Token required for Lead data update',
  'shotx.snipers.form.withoutTitle': 'Please provide the ShotFlow Public ID',
  'shotx.snipers.noApiKeyToken': 'No key found, register your keys in the Settings tab',
  'shotx.snipers.noSniper': 'There is no ShotFlow created in this Workspace!',
  'shotx.snipers.nomeSniper': 'ShotFlow Name (Bot)',
  'shotx.snipers.operator.contains': 'Contains',
  'shotx.snipers.operator.endsWith': 'Ends with',
  'shotx.snipers.operator.equals': 'Equal',
  'shotx.snipers.operator.startsWith': 'Starts with',
  'shotx.snipers.scheduledMessage': 'Service scheduled successfully',
  'shotx.snipers.title': 'ShotFlows',
  'shotx.snipers.typeOperador': 'Type / Operator',
  'shotx.snipers.types.all': 'All',
  'shotx.snipers.types.keyword': 'Keyword',
  'shotx.snipers.value': 'Value',
  'shotx.statuses.close': 'Disconnected',
  'shotx.statuses.connecting': 'Connecting',
  'shotx.statuses.open': 'Connected',
  'shotx.statuses.refused': 'Refused',
  'shotx.tabs.ImportSniper': 'Import ShotFlow',
  'shotx.tabs.config': 'Settings',
  'shotx.tabs.instance': 'Instances',
  'shotx.tabs.sniper': 'ShotFlow',
  'shotx.whatsapp.assignToSniper': 'Assign service to ShotFlow',
  'shotx.whatsapp.assignedToSniper': 'Service assigned to ShotFlow!',
  'shotx.whatsapp.assigningToSniper': 'Assigning service to ShotFlow',
  'shotx.whatsapp.associatedFieldSniper': 'Variable in ShotFlow',
  'shotx.whatsapp.associatedLeadFieldSniper': 'Field to update in lead',
  'shotx.whatsapp.clickToEnd': 'End service',
  'shotx.whatsapp.clickToStart': 'Start',
  'shotx.whatsapp.ended': 'Service ended by @username on @date.',
  'shotx.whatsapp.iWillAnswer': 'I will answer',
  'shotx.whatsapp.instance.alert': 'Changing the instance name will cause the loss of message history.',
  'shotx.whatsapp.instance.body.title': 'Instance',
  'shotx.whatsapp.instance.info': 'Enter a description for the instance.',
  'shotx.whatsapp.instance.title.info': 'Enter the instance name',
  'shotx.whatsapp.instance.title.placeholder': 'WhatsApp Instance Name',
  'shotx.whatsapp.noContact': 'Link a contact with a valid cell phone to send a message.',
  'shotx.whatsapp.noIntegrations': 'No integration configured',
  'shotx.whatsapp.noQuickMessages': 'There is no quick message registered.',
  'shotx.whatsapp.noSniper': 'There is no ShotFlow',
  'shotx.whatsapp.notReady': 'WhatsApp is not ready for use yet',
  'shotx.whatsapp.notStarted': 'You have not started the service yet, click start to respond.',
  'shotx.whatsapp.qrcode.button.disconnect': 'Disconnect',
  'shotx.whatsapp.qrcode.button.refresh': 'Refresh QR Code',
  'shotx.whatsapp.qrcode.info': 'Scan the QR code with your WhatsApp application.',
  'shotx.whatsapp.qrcode.loading': 'Generating QR Code...',
  'shotx.whatsapp.request.createinstance.fail': 'Failed to create instance',
  'shotx.whatsapp.selectSniper': 'Select a ShotFlow',
  'shotx.whatsapp.started': 'Service started by @username on @date.',
  'shotx.whatsapp.status.connected': 'You have a number connected to WhatsApp.',
  'shotx.whatsapp.status.disconnected': 'Not Connected',
  'sidebar.': '',
  'sidebar.404': '404',
  'sidebar.500': '500',
  'sidebar.aboutUs': 'About us',
  'sidebar.account': 'My Account',
  'sidebar.add': 'Add',
  'sidebar.addNew': 'Create',
  'sidebar.agency': 'Agency',
  'sidebar.alerts': 'Alerts',
  'sidebar.analytics': 'Analytics',
  'sidebar.app': 'App',
  'sidebar.applications': 'Applications',
  'sidebar.blank': 'Blank',
  'sidebar.boxed': 'Boxed',
  'sidebar.calendar': 'Calendar',
  'sidebar.cart': 'Cart',
  'sidebar.chat': 'Chat',
  'sidebar.checkout': 'Checkout',
  'sidebar.clients': 'Clients',
  'sidebar.component': 'Components',
  'sidebar.contact': 'Contact',
  'sidebar.crm': 'CRM',
  'sidebar.crypto': 'Crypto',
  'sidebar.dashboard': 'Dashboard',
  'sidebar.dateTimePicker': 'Date & Time Picker',
  'sidebar.dropzone': 'Dropzone',
  'sidebar.ecommerce': 'Ecommerce',
  'sidebar.editor': 'Editor',
  'sidebar.email': 'Email',
  'sidebar.emails': 'Emails',
  'sidebar.event': 'Event',
  'sidebar.events': 'Events',
  'sidebar.extensions': 'Extensions',
  'sidebar.faq(s)': 'Faq(s)',
  'sidebar.features': 'Features',
  'sidebar.feedback': 'Feedback',
  'sidebar.forgotPassword': 'Forgot Password',
  'sidebar.forms': 'Forms',
  'sidebar.funnel': 'Sales Funnel',
  'sidebar.funnels': 'Sales Funnels',
  'sidebar.gallery': 'Gallery',
  'sidebar.general': 'General',
  'sidebar.gettingStarted': 'Getting Started',
  'sidebar.home': 'Home',
  'sidebar.horizontal': 'Horizontal',
  'sidebar.horizontalMenu': 'Horizontal Menu',
  'sidebar.icons': 'Icons',
  'sidebar.imageCropper': 'Image Cropper',
  'sidebar.inbox': 'Inbox',
  'sidebar.invoice': 'Invoice',
  'sidebar.leads': 'Leads',
  'sidebar.level1': 'Level 1',
  'sidebar.level2': 'Level 2',
  'sidebar.listAction': 'List',
  'sidebar.lockScreen': 'Lock Screen',
  'sidebar.login': 'Login',
  'sidebar.mailbox': 'Inbox',
  'sidebar.mailboxes': 'Inbox',
  'sidebar.mailing': 'Mailing',
  'sidebar.miscellaneous': 'Miscellaneous',
  'sidebar.model': 'Model',
  'sidebar.models': 'Models',
  'sidebar.module': 'Module',
  'sidebar.modules': 'Modules',
  'sidebar.multiLevel': 'MultiLevel',
  'sidebar.multilevel': 'Multilevel',
  'sidebar.news': 'News',
  'sidebar.pages': 'Pages',
  'sidebar.pipeline': 'Pipeline',
  'sidebar.pipelines': 'Pipelines',
  'sidebar.plans.dashboard': 'Financial Report',
  'sidebar.plans.warnings': 'Warnings',
  'sidebar.pricing': 'Pricing',
  'sidebar.product': 'Product',
  'sidebar.products': 'Products',
  'sidebar.progress': 'Progress',
  'sidebar.projectDetail': 'Project Details',
  'sidebar.projects': 'Projects',
  'sidebar.promo': 'Promo',
  'sidebar.register': 'Register',
  'sidebar.report': 'Report',
  'sidebar.reports': 'Reports',
  'sidebar.reports.automations.ads': 'Google Ads',
  'sidebar.saas': 'SAAS',
  'sidebar.segmentationsGroup': 'Segmentations',
  'sidebar.session': 'Session',
  'sidebar.shop': 'Shop',
  'sidebar.shopGrid': 'Shop Grid',
  'sidebar.shopList': 'Shop List',
  'sidebar.sublevel': 'Sublevel',
  'sidebar.tables': 'Tables',
  'sidebar.terms&Conditions': 'Terms & Conditions',
  'sidebar.toDo': 'ToDo',
  'sidebar.toggle': 'Show/Hide Sidebar',
  'sidebar.tools': 'Tools',
  'sidebar.user': 'User',
  'sidebar.userList': 'User List',
  'sidebar.userManagement': 'User Management',
  'sidebar.userProfile': 'User Profile',
  'sidebar.users': 'Users',
  'sidebar.videoPlayer': 'Video Player',
  'sniper.botAvailable': 'Option available after connecting your instance',
  'sniper.botDisabled': 'Bot disabled',
  'sniper.botEnabled': 'Bot enabled',
  'sniper.botId': 'ShotFlow public ID',
  'sniper.module.title': 'ShotFlow',
  'sniper.module.tooltip': 'Conversation Automation',
  'sounds.off': 'Disable sounds',
  'sounds.on': 'Enable sounds',
  'stats.CAC': 'Customer Acquisition Cost',
  'stats.QtdDeals': 'Number of Deals',
  'stats.ROI': 'Return on Investment',
  'stats.adsStats': 'Ad Metrics',
  'stats.automationsConvertions': 'Automation Conversions',
  'stats.automationsFired': 'Automation Triggers',
  'stats.average': 'Average',
  'stats.campaignStats': 'Campaign Metrics',
  'stats.campaignsFired': 'Campaign Triggers',
  'stats.cap': 'Capture',
  'stats.conversionAvg': 'Average Conversion Value',
  'stats.conversionCost': 'Conversion Cost',
  'stats.conversionCycle': 'Sales Cycle',
  'stats.conversionCycleAvgTime': 'Average Sales Cycle Time',
  'stats.conversionRate': 'Conversion Rate',
  'stats.conversions': 'Conversions',
  'stats.conversionsChart': 'Business Statistics',
  'stats.conversionsCount': 'Orders Placed',
  'stats.dealInsWonStage': 'Deals Marked as Won',
  'stats.dealsAdded': 'New Deals',
  'stats.dealsAddedValue': 'Value of New Deals',
  'stats.dealsLost': 'Lost Deals',
  'stats.dealsLostRate': 'Lost Deals Rate',
  'stats.dealsLostValue': 'Value of Lost Deals',
  'stats.dealsStats': 'Deal Metrics',
  'stats.dealsWon': 'Won Deals',
  'stats.dealsWonRate': 'Closed Deals Rate',
  'stats.dealsWonValue': 'Value of Won Deals',
  'stats.emailsClickRate': 'Click Rate',
  'stats.emailsOpenCount': 'Open Volume',
  'stats.emailsOpenRate': 'Open Rate',
  'stats.emailsSentCount': 'Sending Volume',
  'stats.emptyMsg': 'There is no data for the period yet',
  'stats.formsSubmitions': 'Forms Filled',
  'stats.funnelsConvertions': 'Funnel Conversions',
  'stats.generated': 'Generated',
  'stats.geoLocation': 'Geolocation',
  'stats.investmentsTotal': 'Total Investment',
  'stats.landingSessions': 'Landing Page Sessions',
  'stats.landingStats': 'Landing Page Metrics',
  'stats.leadsBaseSize': 'Database Size',
  'stats.leadsCap': 'Lead Capture',
  'stats.leadsConverted': 'Customers with Conversions',
  'stats.leadsDailyCap': 'Daily Capture',
  'stats.leadsReached': 'Leads Reached',
  'stats.leadsRegistered': 'Total Capture',
  'stats.lost': 'Lost',
  'stats.mailingStats': 'Sending Metrics',
  'stats.net': 'Net',
  'stats.netRevenue': 'Net Revenue',
  'stats.new': 'New',
  'stats.opportunities': 'Opportunities',
  'stats.sessions': 'Sessions',
  'stats.sessionsCount': 'Total Visits',
  'stats.sessionsIn': 'Sessions in',
  'stats.teamsRanking': 'Team Ranking',
  'stats.teamsStats': 'Team Metrics',
  'stats.ticketsAddedCount': 'Orders Placed',
  'stats.ticketsAverage': 'Average Ticket',
  'stats.ticketsPerDeal': 'Orders per Deal',
  'stats.ticketsTotal': 'Accumulated Revenue',
  'stats.tooltip.CAC': 'Customer Acquisition Costs defines the amount spent by the company to transform a lead into a buyer or a user of the offered services. Calculation Formula: Total cost for customer acquisition / Total new customers = CAC',
  'stats.tooltip.LTV': 'Lifetime Value (LTV) identifies the value that each customer left in your company throughout the period they consumed your products and services. Calculation Formula: (Average Value of a Sale) X (Average Retention Time in months or years for a typical customer) = LTV',
  'stats.tooltip.ROI': 'Return On Investment is an indicator that allows companies to know how much they gained or lost with their investments. Calculation Formula: (Return on investment - investment cost) / investment cost = ROI',
  'stats.total': 'Total',
  'stats.traffic': 'Traffic',
  'stats.trafficSources': 'Traffic Sources',
  'stats.uniqueLeads': 'Unique Leads',
  'stats.uniqueUsers': 'Unique Users',
  'stats.uniqueVisitors': 'Unique Visitors',
  'stats.valueAverage': 'Average Value',
  'stats.visitors': 'Visitors',
  'stats.won': 'Won',
  'stats.wonCycle': 'Deal Cycle',
  'stats.wonCycleAvgTime': 'Average Deal Cycle Time',
  'stats.wonCycleClosingTime': 'Average Completion Time',
  'statuses.active': 'Active',
  'statuses.active.fem': 'Active',
  'statuses.draft': 'Draft',
  'statuses.draft.fem': 'Draft',
  'statuses.inactive': 'Inactive',
  'statuses.inactive.fem': 'Inactive',
  'statuses.model': 'Model',
  'statuses.publish': 'Published',
  'statuses.publish.fem': 'Published',
  'statuses.scheduled': 'Scheduled',
  'statuses.scheduled.fem': 'Scheduled',
  'statuses.send': 'Sent',
  'statuses.send.fem': 'Sent',
  'statuses.sending': 'Sending',
  'statuses.sending.fem': 'Sending',
  'statuses.sent': 'Sent',
  'statuses.sent.fem': 'Sent',
  'statuses.trash': 'Trash',
  'statuses.trash.fem': 'Trash',
  'stock.available': 'Available',
  'stock.initial': 'For the Campaign',
  'stock.label': 'Stock',
  'stock.real': 'Physical Stock',
  'stock.used': 'Used',
  'styles.backgroundColor': 'Background Color',
  'styles.borderColor': 'Border Color',
  'styles.borderWidth': 'Border Width',
  'styles.buttonBackground': 'Button Background Color',
  'styles.buttonColor': 'Button Text Color',
  'styles.buttonOptionsLarge': 'Large',
  'styles.buttonOptionsLeft': 'Left',
  'styles.buttonOptionsMedium': 'Medium',
  'styles.buttonOptionsMiddle': 'Center',
  'styles.buttonOptionsRight': 'Right',
  'styles.buttonOptionsSmall': 'Small',
  'styles.buttonPosition': 'Button Position',
  'styles.buttonText': 'Button Text',
  'styles.customCSS': 'Custom CSS',
  'styles.fieldBackground': 'Field Background Color',
  'styles.fieldBorderColor': 'Field Border Color',
  'styles.fieldColor': 'Field Color',
  'styles.labelColor': 'Label Color',
  'styles.sizeButton': 'Button Size',
  'subscription.billing': 'Billing',
  'subscription.boleto': 'Bank Slip',
  'subscription.canceled': 'Canceled',
  'subscription.credit_card': 'Credit Card',
  'subscription.ended': 'Ended',
  'subscription.overview.amount': 'Amount',
  'subscription.overview.bankSlips': 'Bank Slips',
  'subscription.overview.canceled': 'Canceled',
  'subscription.overview.canceleds': 'Canceled',
  'subscription.overview.count': 'Quantity',
  'subscription.overview.creditCards': 'Credit Cards',
  'subscription.overview.customer': 'Customer',
  'subscription.overview.dailyRevenues': 'Daily Revenue',
  'subscription.overview.geographicDistribution': 'Geographic Distribution',
  'subscription.overview.list': 'Subscriptions',
  'subscription.overview.monthlyRevenues': 'Monthly Revenue',
  'subscription.overview.paid': 'Paid',
  'subscription.overview.paids': 'Paid',
  'subscription.overview.paymentMethod': 'Payment Method',
  'subscription.overview.period': 'Period',
  'subscription.overview.plan': 'Plan',
  'subscription.overview.plansTotals': 'Totals by Plan',
  'subscription.overview.state': 'State',
  'subscription.overview.status': 'Status',
  'subscription.overview.statusDistribution': 'Distribution by Status',
  'subscription.overview.subscriptionsEvolution': 'Subscription Evolution',
  'subscription.overview.total': 'Active subscriptions',
  'subscription.overview.totalRevenues': 'Total Revenue',
  'subscription.overview.unpaid': 'Pending',
  'subscription.overview.unpaids': 'Pending',
  'subscription.paid': 'Paid',
  'subscription.pending_payment': 'Pending',
  'subscription.status': 'Subscription Status',
  'subscription.trialing': 'Trial',
  'subscription.unpaid': 'Unpaid',
  'tables.loadingText': 'Loading...',
  'tables.nextText': 'Next',
  'tables.noDataText': 'No items to display',
  'tables.ofText': 'of',
  'tables.pageText': 'Page',
  'tables.previousText': 'Previous',
  'tables.rowsText': '',
  'tables.summaryText': 'Page {from} of {count}',
  'tags.dealTags': 'Deal Tags',
  'tags.leadTags': 'Lead Tags',
  'task.add': 'New Task',
  'task.clone': 'Clone Task',
  'task.create': 'Create Task',
  'task.edit': 'Edit Task',
  'tasklists.label.importance': 'Weight',
  'tasklists.label.title': 'Title',
  'tasklists.task_question.import': 'Import Questionnaire',
  'tasks.clone': 'Clone Tasks',
  'tasks.edit': 'Edit Tasks',
  'texts.AddItemsToStartActions': 'Drag actions here',
  'texts.AddItemsToStartTriggers': 'Drag triggers here',
  'texts.actions': 'Actions',
  'texts.addNewPost': 'Create [%s]',
  'texts.addVideoPoster': 'Add Poster to Video',
  'texts.affiliateHomeLink': 'Affiliate Home Link',
  'texts.affiliateLink': 'Affiliate Link',
  'texts.allActions': 'All Actions',
  'texts.alreadyHaveAccount': 'Already have an account?',
  'texts.alreadyHavingAccountSignIn': 'Already have an account? Sign in',
  'texts.and': 'and',
  'texts.attachments': 'Attachments',
  'texts.banner': 'Banner',
  'texts.banners': 'Banners',
  'texts.bannersKit': 'Banners Kit',
  'texts.campaign': 'Campaign',
  'texts.cannotBeUndone': 'This action cannot be undone',
  'texts.changeMyPlan': 'Change my plan',
  'texts.check-in': 'check-in',
  'texts.checkin': 'Check-in',
  'texts.checkin.plural': 'Check-ins',
  'texts.choices': 'Choices',
  'texts.chooseAnotherPlan': 'Choose another plan',
  'texts.chooseYourPlan': 'Choose your plan',
  'texts.clickHereToSetup': 'Click here to setup',
  'texts.clickHereToStart': 'Click here to start',
  'texts.column': 'Column',
  'texts.columns': 'Columns',
  'texts.commaSeparatedEmails': 'Comma-separated email addresses',
  'texts.commaSeparatedTags': 'Comma-separated tags',
  'texts.comments': 'Comments',
  'texts.completed': 'Completed',
  'texts.confirmAllFieldsRemoval': 'Do you want to delete all fields?',
  'texts.confirmFieldRemoval': 'Do you want to delete this field?',
  'texts.confirmed': 'Confirmed',
  'texts.confirmed.plural': 'Confirmed',
  'texts.content': 'Content',
  'texts.contentType': 'Content Type',
  'texts.conversion': 'Conversion',
  'texts.conversionGoals': 'Conversion Goals',
  'texts.conversions': 'Conversions',
  'texts.copiedToClipboard': 'Copied to clipboard',
  'texts.copy': 'Copy',
  'texts.copyAffiliateHomeLink': 'Copy Affiliate Home Link',
  'texts.copyAffiliateLink': 'Copy Affiliate Link',
  'texts.copyEmbedCode': 'Copy HTML Embed Code',
  'texts.copyImageUrl': 'Copy Image URL',
  'texts.copyParentLink': 'Copy Parent Link',
  'texts.copyToClipboard': 'Copy',
  'texts.copyVideoUrl': 'Copy Video URL',
  'texts.count': 'Count',
  'texts.customBanners': 'Custom Banners',
  'texts.customVideos': 'Custom Videos',
  'texts.customer': 'Customer',
  'texts.customers': 'Customers',
  'texts.customizeYourPlan': 'Customize your plan',
  'texts.data': 'Data',
  'texts.date': 'Date',
  'texts.dateRegistered': 'Registration Date',
  'texts.dates': 'Dates',
  'texts.deadline': 'Deadline',
  'texts.dealTitle': 'Deal Title',
  'texts.description': 'Description',
  'texts.discardImage': 'Discard Image',
  'texts.discardVideo': 'Discard Video',
  'texts.document': 'Document',
  'texts.documents': 'Documents',
  'texts.dontHaveAccount': 'Don\'t have an account yet?',
  'texts.dropItemsHere': 'Drag and drop items here',
  'texts.email': 'Email',
  'texts.emails': 'Emails',
  'texts.embed': 'Embed',
  'texts.embedCreated': 'Embed created successfully!',
  'texts.enable': 'Enable',
  'texts.end': 'End',
  'texts.enterPasswords': 'Passwords',
  'texts.enterYourEmail': 'Your Email',
  'texts.enterYourName': 'Your Name',
  'texts.enterYourPassword': 'Enter your Password',
  'texts.equal_mode': 'Equitable',
  'texts.fetchingItems': 'Loading...',
  'texts.fillActionData': 'Complete the action fields',
  'texts.filterAction': 'Filter',
  'texts.filterBy': 'Filter by',
  'texts.filterChoices': 'Filter Choices',
  'texts.filterValues': 'Filter Selected',
  'texts.forgotPassword': 'Forgot password',
  'texts.goal': 'Goal',
  'texts.goalName': 'Goal Name',
  'texts.goalTarget': 'Assign Goal to',
  'texts.goalType': 'Goal Type',
  'texts.goals': 'Goals',
  'texts.graphicPieces': 'Graphic Pieces',
  'texts.hi': 'Hi',
  'texts.historial': 'History',
  'texts.if': 'If',
  'texts.image': 'Image',
  'texts.images': 'Images',
  'texts.importBannersKit': 'Import Default Banners Kit',
  'texts.importSuccess': 'Import completed successfully',
  'texts.in': 'in',
  'texts.informYourDomain': 'Enter your domain',
  'texts.insertTitle': 'Enter the title',
  'texts.instructions': 'Instructions',
  'texts.investmentName': 'Investment Name',
  'texts.investments': 'Investments',
  'texts.investmentsOnPeriod': 'Investments in Period',
  'texts.lead': 'Lead',
  'texts.leads': 'Leads',
  'texts.leadsGoals': 'Lead Goals',
  'texts.leaveBlank': 'Leave blank if not applicable',
  'texts.listAction': 'List',
  'texts.listAll': 'List all',
  'texts.loading': 'Loading...',
  'texts.manager': 'Account Manager',
  'texts.managers': 'Account Managers',
  'texts.mktBanner': 'Promotional Banner',
  'texts.mktBanners': 'Promotional Banners',
  'texts.mktMaterial': 'Promotional Material',
  'texts.mktVideo': 'Promotional Video',
  'texts.mktVideos': 'Promotional Videos',
  'texts.montlyPlan': 'Monthly Plan',
  'texts.myPlan': 'My plan',
  'texts.new': 'New',
  'texts.newDeal': 'New Deal',
  'texts.newPost': 'New Item',
  'texts.newRegisters': 'New',
  'texts.newUser': 'New user',
  'texts.noItemsAvailable': 'No items available',
  'texts.noItemsSelected': 'No items selected',
  'texts.noOperationsToPerform': 'No operations to perform',
  'texts.none': 'None',
  'texts.none.f': 'None',
  'texts.option': 'Option',
  'texts.optional': 'Optional',
  'texts.options': 'Options',
  'texts.or': 'or',
  'texts.orSigninWith': 'or sign in with',
  'texts.other': 'Other',
  'texts.otherActions': 'Other Actions',
  'texts.others': 'Others',
  'texts.parentLink': 'Tree Business Link',
  'texts.parentTitle': 'Tree Business',
  'texts.participant': 'Participant',
  'texts.participants': 'Participants',
  'texts.passwordResetEmailSent': 'Email sent successfully',
  'texts.payment': 'Payment',
  'texts.payments': 'Payments',
  'texts.platform': 'Platform',
  'texts.platforms': 'Platforms',
  'texts.preferences': 'Preferences',
  'texts.privateToken': 'Private Token',
  'texts.processing': 'Processing',
  'texts.promoter': 'Promoter',
  'texts.promoters': 'Promoters',
  'texts.qiplusUrlType': 'Page Type',
  'texts.qiusers': 'Users',
  'texts.random_mode': 'Random',
  'texts.redirect': 'Redirect',
  'texts.redirectAction': 'Redirect',
  'texts.redirectType': 'Redirect Type',
  'texts.refreshToken': 'Refresh Token',
  'texts.relate': 'Relate',
  'texts.related': 'Related',
  'texts.reloadSection': 'Reload',
  'texts.removeImage': 'Remove Image',
  'texts.removeImageSize': 'Remove this Image Size',
  'texts.removeVideo': 'Remove Video',
  'texts.removeVideoSize': 'Remove this Video Size',
  'texts.resetPassword': 'Reset Password',
  'texts.result': 'Result',
  'texts.results': 'Results',
  'texts.script': 'Script',
  'texts.scripts': 'Scripts',
  'texts.select': 'Select',
  'texts.select.intance': 'Select your instance',
  'texts.select.quickMessage': 'Select a quick message',
  'texts.selectAStage': 'Select a stage',
  'texts.selectATrigger': 'Select a trigger',
  'texts.selectAnAction': 'Select an action',
  'texts.selectAnItem': 'Select an item',
  'texts.selectDocument': 'Select a document',
  'texts.selectImage': 'Select image',
  'texts.selectOwner': 'Select an account',
  'texts.selected': 'Selected',
  'texts.selectedPlural': 'Selected',
  'texts.seller': 'Seller',
  'texts.sellers': 'Sellers',
  'texts.shortlink': 'Shortlink',
  'texts.shortlinkCreated': 'Shortlink created successfully!',
  'texts.source': 'Source',
  'texts.sources': 'Sources',
  'texts.stage': 'Stage',
  'texts.stages': 'Stages',
  'texts.start': 'Start',
  'texts.status': 'Status',
  'texts.store': 'Store',
  'texts.stores': 'Stores',
  'texts.submitedScripts': 'Scripts on confirmation page',
  'texts.subscribed': 'Subscribed',
  'texts.subscribed.plural': 'Subscribed',
  'texts.tags': 'Tags',
  'texts.tasklistLost': 'Lost',
  'texts.tasklistWon': 'Won',
  'texts.termsAndConditions': 'Terms and Conditions',
  'texts.ticketItems': 'Order Items',
  'texts.token': 'Token',
  'texts.tracking': 'Tracking',
  'texts.treeBusinessTitle': 'Tree Business',
  'texts.trialDays': 'Trial Days',
  'texts.trigger': 'Trigger',
  'texts.triggers': 'Triggers',
  'texts.tutorial': 'Tutorial',
  'texts.type': 'Type',
  'texts.unit': 'Unit',
  'texts.units': 'Units',
  'texts.updateDeal': 'Edit Deal',
  'texts.updateUser': 'Edit user',
  'texts.updateYourPlan': 'Update your plan',
  'texts.updated': 'Updated',
  'texts.updated.plural': 'Updated',
  'texts.updatedRegisters': 'Updated',
  'texts.userGoodbye': 'See you next time!',
  'texts.userLogoutSuccess': 'See you next time!',
  'texts.userWelcome': 'Welcome',
  'texts.usersIncluded': 'Users Included',
  'texts.value': 'Value',
  'texts.video': 'Video',
  'texts.videoPoster': 'Poster',
  'texts.wantThisPlan': 'I want this plan',
  'texts.yearlyPlan': 'Yearly Plan',
  'themeOptions.appSettings': 'App Settings',
  'themeOptions.boxLayout': 'Box Layout',
  'themeOptions.darkMode': 'Dark Vision',
  'themeOptions.gridLayout': 'Layout: Grid',
  'themeOptions.listLayout': 'Layout: List',
  'themeOptions.miniSidebar': 'Compact Menu',
  'themeOptions.rtlLayout': 'Rtl Layout',
  'themeOptions.sidebarBackgroundImages': 'Sidebar Background Images',
  'themeOptions.sidebarDark': 'Dark',
  'themeOptions.sidebarImage': 'Sidebar Image',
  'themeOptions.sidebarLight': 'Light',
  'themeOptions.sidebarOverlay': 'Sidebar Overlay',
  'themeOptions.themeColor': 'Theme Color',
  'time.after': 'After',
  'time.afterStart': 'After start',
  'time.before': 'Before',
  'time.beforeStart': 'Before start',
  'time.days': 'days',
  'time.deadline': 'Deadline',
  'time.hours': 'hours',
  'time.invalidDateMessage': 'Invalid date',
  'time.maxDateMessage': 'The date is greater than the maximum allowed',
  'time.minDateMessage': 'The date is less than the minimum allowed',
  'time.minutes': 'minutes',
  'time.onadd': 'After being added',
  'time.onadd.fem': 'After being added',
  'time.oncreate': 'After creation',
  'time.oncreate.fem': 'After creation',
  'time.time': 'Time',
  'tips.saveOrUseOnlyInThisForm': 'You can use this field only in this form or save it in the database',
  'triggers.addFromShotX': 'Lead added through ShotX',
  'triggers.added': 'New',
  'triggers.addedAsParticipant': 'Was added as participant',
  'triggers.addedToFunnel': 'Entry into funnel',
  'triggers.addedToSegmentation': 'Was added to segmentation',
  'triggers.addedToStore': 'Was added to store',
  'triggers.addedViaIntegration': 'Was added through integration',
  'triggers.added_as_participant': 'Participant Added',
  'triggers.added_to_funnel': 'Entry into funnel',
  'triggers.added_to_segmentation': 'Added to segmentation',
  'triggers.added_to_store': 'Added to store',
  'triggers.added_via_integration': 'Added via integration',
  'triggers.ageRange': 'By Age Range',
  'triggers.answeredAQuestionnaire': 'Answered the questionnaire',
  'triggers.birthdayMonth': 'By Birthday Month',
  'triggers.bought': 'Purchase',
  'triggers.boughtAProduct': 'Purchased the product',
  'triggers.canceled': 'Canceled',
  'triggers.cep': 'By ZIP code range',
  'triggers.checkedIn': 'Did check-in',
  'triggers.city': 'By city',
  'triggers.clickedAnEmail': 'Clicked on Email',
  'triggers.completed': 'Completed',
  'triggers.confirmedParticipation': 'Confirmed participation',
  'triggers.confirmed_participant': 'Participation Confirmed',
  'triggers.converted': 'Conversion',
  'triggers.convertedInFunnel': 'Converted in funnel',
  'triggers.country': 'By country',
  'triggers.currentStageInFunnel': 'Is in the stage',
  'triggers.deals_added': 'Deals Added',
  'triggers.didNotClickAnEmail': 'Did not click on email',
  'triggers.didNotOpenEmail': 'Did not open email',
  'triggers.didntCheckin': 'Did not check-in',
  'triggers.filledAForm': 'Filled out the form',
  'triggers.fired': 'Fired',
  'triggers.gender': 'By Gender',
  'triggers.impression': 'Impression',
  'triggers.interaction_added': 'Interaction linked to a Lead',
  'triggers.leadGenerated': 'Lead Generated',
  'triggers.leads_added': 'Leads Generated',
  'triggers.lost': 'Lost Deals',
  'triggers.movedToLost': 'Marked as Lost',
  'triggers.movedToWon': 'Marked as Won',
  'triggers.neighborhood': 'By neighborhood',
  'triggers.neverBought': 'Never purchased',
  'triggers.neverBoughtAProduct': 'Never purchased the product',
  'triggers.neverConverted': 'Never Converted',
  'triggers.notDealExistActive': 'No active deal exists',
  'triggers.openedAnEmail': 'Opened the Email',
  'triggers.played': 'Plays',
  'triggers.progressed': 'Progression in funnel',
  'triggers.progressedInFunnel': 'Progressed in funnel',
  'triggers.range': 'Define the range',
  'triggers.receivedAMessage': 'For each Message Received',
  'triggers.receivedAnEmail': 'Received the Email',
  'triggers.regressed': 'Regression in funnel',
  'triggers.removed': 'Removed',
  'triggers.removedFromSegmentation': 'Was removed from segmentation',
  'triggers.removedFromStore': 'Was removed from store',
  'triggers.removedFromarticipants': 'Was removed from participants',
  'triggers.removed_from_participants': 'Removed from participants',
  'triggers.removed_from_segmentation': 'Removed from segmentation',
  'triggers.removed_from_store': 'Removed from store',
  'triggers.stage': 'Is in the stage',
  'triggers.stageIn': 'Entry into stage',
  'triggers.stageInFunnel': 'Entered the stage',
  'triggers.stageOut': 'Exit from stage',
  'triggers.stageOutFunnel': 'Left the stage',
  'triggers.state': 'By state',
  'triggers.submited': 'Form Submitted',
  'triggers.tagAdded': 'The tag was added',
  'triggers.tagHasNot': 'Does not have the tag',
  'triggers.tagHasTag': 'Has the tag',
  'triggers.tagRemoved': 'The tag was removed',
  'triggers.tag_added': 'Tag added',
  'triggers.tag_removed': 'Tag removed',
  'triggers.tasklistCompleted': 'Completed the task list',
  'triggers.ticketCancelled': 'Ticket Canceled',
  'triggers.ticketGenerated': 'Ticket Generated',
  'triggers.tickets_canceled': 'Canceled',
  'triggers.triggerCol': 'Conditions Group',
  'triggers.unconfirmedParticipation': 'Unconfirmed participation',
  'triggers.unconfirmed_participant': 'Participation Unconfirmed',
  'triggers.undidCheckin': 'The check-in was canceled',
  'triggers.unload': 'Page Exit',
  'triggers.updated': 'Updated',
  'triggers.updatedViaIntegration': 'Was updated through integration',
  'triggers.updated_via_integration': 'Updated via integration',
  'triggers.viewed': 'Viewed',
  'triggers.visited': 'Visited',
  'triggers.won': 'Won Deals',
  'videos.customVideoUrl': 'Video URL',
  'videos.dailymotionVideo': 'Dailymotion Video',
  'videos.externalVideo': 'Hosted Video URL',
  'videos.facebookVideo': 'Facebook Video',
  'videos.fileVideo': 'Video File',
  'videos.twitchVideo': 'Twitch Video',
  'videos.videoProvider': 'Video Provider',
  'videos.vimeoVideo': 'Vimeo Video',
  'videos.youtubeVideo': 'Youtube Video',
  'whatsapp.authenticated': 'Authenticated',
  'whatsapp.connectQiplus': 'Connect QIPlus with your WhatsApp account',
  'whatsapp.connected': 'Connected',
  'whatsapp.connectedAs': 'Connected as',
  'whatsapp.createNewAccount': 'Create new account',
  'whatsapp.deleted': 'Message deleted',
  'whatsapp.edited': 'Edited',
  'whatsapp.fullMobileNumber': 'Mobile number with country code and area code',
  'whatsapp.generatingQrCode': 'Generating QR Code...',
  'whatsapp.inputMessage.placeholder': 'Type a message',
  'whatsapp.inputMessage.tooltip': 'If you wish, use [Ctrl + Enter] to send.',
  'whatsapp.me': 'Me',
  'whatsapp.message.send.fail': 'Failed to send.',
  'whatsapp.message.send.resend': 'Click to try resending this message.',
  'whatsapp.message.send.resend.fail': 'Failed to resend message.',
  'whatsapp.send': 'Send',
  'whatsapp.settingYourAccount': 'Preparing your account...',
  'whatsapp.successfullyConnectedQiplus': 'Congratulations! QIPlus is connected to your WhatsApp account',
  'whatsapp.waitingForQrCodeScan': 'Open WhatsApp on your phone and scan the QR Code to log in',
  'widgets.AcceptorrRejectWithin': 'Accept or reject within',
  'widgets.ActionsBuilder': 'Select actions',
  'widgets.ActionsSelector': 'Select',
  'widgets.ComposeEmail': 'Compose Email',
  'widgets.InteractiveLists': 'Interactive Lists',
  'widgets.LiveChatSupport': 'Live Chat Support',
  'widgets.Mega': 'Mega',
  'widgets.ModulesPostsSelector': 'Select',
  'widgets.MutltiSelectList': 'Mutlti Select List',
  'widgets.OngoingProjects': 'Ongoing Projects',
  'widgets.PostsSelector': 'Select',
  'widgets.ProjectStatus': 'Project Status',
  'widgets.QuickLinks': 'Quick Links',
  'widgets.RecentOrders': 'Recent Orders',
  'widgets.ShareWithFriends': 'Share!',
  'widgets.Simple App Bars': 'Simple App Bars',
  'widgets.TriggersBuilder': 'Select triggers',
  'widgets.VerticalStyleCheckbox': 'Vertical Style Checkbox',
  'widgets.aboutUs': 'About Us',
  'widgets.action': 'Action',
  'widgets.activeUsers': 'Active Users',
  'widgets.activitis': 'Activities',
  'widgets.activity': 'Activity',
  'widgets.activityBoard': 'Activity Board',
  'widgets.addNew': 'Create',
  'widgets.addToCart': 'Add to Order',
  'widgets.additionalContent': 'Additional Content',
  'widgets.admin': 'Admin',
  'widgets.adminTheme': 'Admin Theme',
  'widgets.advanced': 'Advanced',
  'widgets.advancedGridLists': 'Advanced Grid Lists',
  'widgets.advancedSearch': 'Advanced Search',
  'widgets.agenda': 'Agenda',
  'widgets.alertDialog': 'Alert Dialog',
  'widgets.alertDismiss': 'Alert Dismiss',
  'widgets.alertsWithIcons': 'Alerts With Icons',
  'widgets.alertsWithLink': 'Alerts With Link',
  'widgets.all': 'All',
  'widgets.alreadyHavingAccountLogin': 'Already Having Account Login',
  'widgets.anchorPlayGround': 'Anchor Play Ground',
  'widgets.animatedSlideDialogs': 'Animated Slide Dialogs',
  'widgets.anotherLink': 'Another Link',
  'widgets.api': 'Api',
  'widgets.app': 'App',
  'widgets.appBarsWithButtons': 'App Bars With Buttons',
  'widgets.appNotifications': 'App Notifications',
  'widgets.appearOrder': 'Appearance Order',
  'widgets.apply': 'Apply',
  'widgets.approve': 'Approve',
  'widgets.areaChart': 'Area Chart',
  'widgets.assignTeam': 'Assign Team',
  'widgets.author': 'Editor',
  'widgets.autoAssignTeam': 'Automatically Assign Team',
  'widgets.autoComplete': 'Auto Complete',
  'widgets.backend': 'Backend',
  'widgets.backgroundVarient': 'Background Varient',
  'widgets.badgeLinks': 'Badge Links',
  'widgets.badgeWithHeadings': 'Badge With Headings',
  'widgets.bandwidthUse': 'Bandwidth Use',
  'widgets.barChart': 'Bar Chart',
  'widgets.baseConfig': 'Base Config',
  'widgets.basic': 'Basic',
  'widgets.basicAlert': 'Basic Alert',
  'widgets.basicCalendar': 'Basic Calendar',
  'widgets.basicCalender': 'Basic Calender',
  'widgets.basicTab': 'Basic Tab',
  'widgets.basicTable': 'Basic Table',
  'widgets.booking': 'Booking',
  'widgets.bounced': 'Bounced',
  'widgets.browse': 'Browse',
  'widgets.bubbleChart': 'Bubble Chart',
  'widgets.buffer': 'Buffer',
  'widgets.buttonNavigation': 'Button Navigation',
  'widgets.buttonNavigationWithNoLabel': 'button Navigation With No Label',
  'widgets.buttonOutline': 'Button Outline',
  'widgets.buttonSize': 'Button Size',
  'widgets.buttonState': 'Button State',
  'widgets.buttonWithIconAndLabel': 'Button With Icon And Label',
  'widgets.buyMore': 'Continue Shopping',
  'widgets.byDay': 'Per Day',
  'widgets.byMonth': 'Per Month',
  'widgets.byWeek': 'Per Week',
  'widgets.byYear': 'Per Year',
  'widgets.campaignPerformance': 'Campaign Performance',
  'widgets.cancelled': 'Cancelled',
  'widgets.cardGroup': 'Card Group',
  'widgets.cardLink': 'Card Link',
  'widgets.cardOutline': 'Card Outline',
  'widgets.cardSubtitle': 'card Subtitle',
  'widgets.cardTitle': 'Card Title',
  'widgets.category': 'Category',
  'widgets.centeredLabels': 'Centered Labels',
  'widgets.centeredTabs': 'Centered Tabs',
  'widgets.change': 'Change',
  'widgets.changeTransition': 'Change Transition',
  'widgets.checkboxListControl': 'Checkbox List Control',
  'widgets.checklist': 'Checklist',
  'widgets.checklistQuestionary': 'Checklist Questionnaire',
  'widgets.chipArray': 'Chip Array',
  'widgets.chipWithAvatar': 'Chip With Avatar',
  'widgets.chipWithClickEvent': 'Chip With Click Event',
  'widgets.chipWithIconAvatar': 'Chip With Icon Avatar',
  'widgets.chipWithTextAvatar': 'Chip With Text Avatar',
  'widgets.circularProgressBottomStart': 'Circular Progress Bottom Start',
  'widgets.code': 'Code',
  'widgets.color': 'Color',
  'widgets.commments': 'Commments',
  'widgets.company': 'Company',
  'widgets.companyName': 'Company',
  'widgets.comparePlans': 'Compare our plans',
  'widgets.components': 'Components',
  'widgets.componet': 'Component',
  'widgets.composeMail': 'New Email',
  'widgets.conference': 'Conference',
  'widgets.confirmationDialogs': 'Confirmation Dialogs',
  'widgets.confirmed': 'Confirmed',
  'widgets.contextualColoredTable': 'Contexual Colored Table',
  'widgets.contexualAlerts': 'Contexual Alerts',
  'widgets.contexualColoredSnackbars': 'Contexual Colored Snackbars',
  'widgets.contexualVariations': 'Contexual Variations',
  'widgets.controlledAccordion': 'Controlled Accordion',
  'widgets.croppedImage': 'Cropped Image',
  'widgets.culturesCalendar': 'Cultures Calendar',
  'widgets.culturesCalender': 'Cultures Calender',
  'widgets.currentDate': 'Current Date',
  'widgets.currentTime': 'Current Time',
  'widgets.customCalender': 'Custom Calendar',
  'widgets.customClickableChip': 'Custom Clickable Chip',
  'widgets.customColor': 'Custom Color',
  'widgets.customColorCheckbox': 'Custom Color Checkbox',
  'widgets.customControlBar': 'Custom Control Bar',
  'widgets.customDeleteIconChip': 'Custom Delete Icon Chip',
  'widgets.customIconAlert': 'Custom Icon Alert',
  'widgets.customPicker': 'Custom Picker',
  'widgets.customRendering': 'Custom Rendering',
  'widgets.customStyleAlert': 'Custom Style Alert',
  'widgets.daily': 'Daily',
  'widgets.dailySales': 'Daily Sales',
  'widgets.dataTable': 'Data Table',
  'widgets.dataUse': 'Data Use',
  'widgets.date': 'Date',
  'widgets.dateAndTimePicker': 'Date And Time Picker',
  'widgets.dateCreated': 'Creation Date',
  'widgets.dateModified': 'Modification Date',
  'widgets.dates': 'Dates',
  'widgets.days_28': '28 days',
  'widgets.deadline': 'Deadline',
  'widgets.defaultDatePicker': 'Default Date Picker',
  'widgets.defaultPicker': 'Default Picker',
  'widgets.defualtReactForm': 'Defualt React Form',
  'widgets.deletableChip': 'Deletable Chip',
  'widgets.deleted': 'Deleted',
  'widgets.description': 'Description',
  'widgets.descriptionAlert': 'Description Alert',
  'widgets.designation': 'Designation',
  'widgets.determinate': 'Determinate',
  'widgets.dialogs': 'Dialogs',
  'widgets.disableChip': 'Disable Chip',
  'widgets.disabledCheckbox': 'Disabled Checkbox',
  'widgets.disabledRadio': 'Disabled Radio',
  'widgets.discoverPeople': 'Discover People',
  'widgets.done': 'Done',
  'widgets.doughnut': 'Doughnut',
  'widgets.downshiftAutoComplete': 'Downshift Auto Complete',
  'widgets.dragAndDropCalendar': 'Drag And Drop Calendar',
  'widgets.dragAndDropCalender': 'Drag And Drop Calender',
  'widgets.dragula': 'Dragula',
  'widgets.emailsStatistics': 'Email Statistics',
  'widgets.employeeList': 'Employee List',
  'widgets.endDate': 'End',
  'widgets.enterYourPassword': 'Enter your Password',
  'widgets.enterpriseEdition': 'Enterprise Edition',
  'widgets.expenseCategory': 'Expense Category',
  'widgets.expenses': 'Expenses',
  'widgets.exportToExcel': 'Export to Excel',
  'widgets.externalUrl': 'External URL',
  'widgets.faq(s)': 'Faq(s)',
  'widgets.file': 'File',
  'widgets.filters': 'Filters',
  'widgets.fixedTabs': 'Fixed Tabs',
  'widgets.flatButtons': 'Flat Buttons',
  'widgets.floatingActionButtons': 'Floating Action Buttons',
  'widgets.folderLists': 'Folder Lists',
  'widgets.follow': 'Follow',
  'widgets.follower': 'Follower',
  'widgets.following': 'Following',
  'widgets.forcedScrolledButtons': 'Forced Scrolled Buttons',
  'widgets.forgetPassword': 'Forget Password',
  'widgets.formDialogs': 'Form Dialogs',
  'widgets.formGrid': 'Form Grid',
  'widgets.formValidate': 'Form Validate',
  'widgets.formValidation': 'Form Validation',
  'widgets.formattedInputs': 'Formatted Inputs',
  'widgets.free': 'Free',
  'widgets.frequentlyAskedQuestions': 'Frequently Asked Questions',
  'widgets.frontend': 'Frontend',
  'widgets.fullDescription': 'Full Description',
  'widgets.fullScreenDialogs': 'Full Screen Dialogs',
  'widgets.gallery': 'Gallery',
  'widgets.global': 'Global',
  'widgets.helpButtonTooltip': 'Help',
  'widgets.helpToShareText': 'Help us spread the world by sharing our website with your friends and followers on social media!',
  'widgets.hiddenLabels': 'Hidden Labels',
  'widgets.high': 'High',
  'widgets.horizontalBar': 'Horizontal Bar',
  'widgets.horizontalLinear': 'Horizontal Linear',
  'widgets.horizontalLinearAlternativeLabel': 'Horizontal Linear Alternative Label',
  'widgets.horizontalLinerAlternativeLabel': 'Horizontal Liner Alternative Label',
  'widgets.horizontalNonLinear': 'Horizontal Non Linear',
  'widgets.horizontalNonLinearAlternativeLabel': 'Horizontal Non Linear Alternative Label',
  'widgets.horizontalNonLinerAlternativeLabel': 'Horizontal Non Liner Alternative Label',
  'widgets.horizontalStyleCheckbox': 'Horizontal Style Checkbox',
  'widgets.howWouldYouRateUs': 'How would you rate us?',
  'widgets.httpLiveStreaming': 'HTTP Live Streaming',
  'widgets.iconButton': 'Icon Button',
  'widgets.iconNavigation': 'Icon Navigation',
  'widgets.iconWithLabel': 'Icon With Label',
  'widgets.iconsAvatars': 'Icons Avatars',
  'widgets.iconsTabs': 'Icons Tabs',
  'widgets.imageAvatars': 'Image Avatars',
  'widgets.imageOnlyGridLists': 'Image Only Grid Lists',
  'widgets.important': 'Important',
  'widgets.indeterminate': 'Indeterminate',
  'widgets.inlineForm': 'Inline Form',
  'widgets.inputGridSizing': 'Input Grid Sizing',
  'widgets.inputSizing': 'Input Sizing',
  'widgets.inputWithDanger': 'Input With Danger',
  'widgets.inputWithSuccess': 'Input With Success',
  'widgets.insetDividers': 'Inset Dividers',
  'widgets.insetLists': 'Inset Lists',
  'widgets.interactiveIntegration': 'Interactive Integration',
  'widgets.interminateSelection': 'Interminate Selection',
  'widgets.issue': 'Issue',
  'widgets.keyboardShortcuts': 'Keyboard Shortcuts',
  'widgets.lastMonth': 'Last Month',
  'widgets.lastWeek': 'Last Week',
  'widgets.latestPost': 'Latest Post',
  'widgets.layouts': 'Layouts',
  'widgets.lettersAvatars': 'Letters Avatars',
  'widgets.lifetime': 'Lifetime',
  'widgets.lineBarAreaChart': 'Line Bar Area Chart',
  'widgets.lineChart': 'Line Chart',
  'widgets.linearProgressLineBar': 'Linear Progress Line Bar',
  'widgets.listDividers': 'List Dividers',
  'widgets.listItemWithImage': 'List Item With Image',
  'widgets.listing': 'Listing',
  'widgets.lockScreen': 'Lock Screen',
  'widgets.logIn': 'Log In',
  'widgets.logOut': 'Log Out',
  'widgets.logs': 'Logs',
  'widgets.low': 'Low',
  'widgets.mail': 'Mail',
  'widgets.mailing.grapesjs.copy': "Variable '[%val]' copied to clipboard. Use 'CTRL+V' to paste",
  'widgets.master': 'Master',
  'widgets.materialBadge': 'Material Badge',
  'widgets.maxHeightMenu': 'Max Height Menu',
  'widgets.message': 'Message',
  'widgets.messages': 'Messages',
  'widgets.monthly': 'Monthly',
  'widgets.multiSelectList': 'Multi Select List',
  'widgets.multilevel': 'Multilevel',
  'widgets.multipleTabs': 'Multiple Tabs',
  'widgets.myProfile': 'My Profile',
  'widgets.nativeSelect': 'Native Select',
  'widgets.nestedLists': 'Nested Lists',
  'widgets.netProfit': 'Net Profit',
  'widgets.new': 'New',
  'widgets.new.plural': 'New',
  'widgets.newCustomers': 'New Customers',
  'widgets.newEmails': 'New Mails',
  'widgets.noLogsYet': 'No logs yet',
  'widgets.note': 'Note',
  'widgets.notifications': 'Notifications',
  'widgets.number': 'Number',
  'widgets.occupation': 'Occupation',
  'widgets.onlineSources': 'Online Sources',
  'widgets.onlineVistors': 'Visitors',
  'widgets.open': 'Open',
  'widgets.openAlertDialog': 'Open Alert Dialog',
  'widgets.openBottom': 'Open Bottom',
  'widgets.openFormDialog': 'Open Form Dialog',
  'widgets.openLeft': 'Open Left',
  'widgets.openResponsiveDialog': 'Open Responsive Dialog',
  'widgets.openRight': 'Open Right',
  'widgets.openSimpleDialog': 'Open Simple Dialog',
  'widgets.openTop': 'Open Top',
  'widgets.optionA': 'Option A',
  'widgets.optionB': 'Option B',
  'widgets.optionC': 'Option C',
  'widgets.optionM': 'Option M',
  'widgets.optionN': 'Option N',
  'widgets.optionO': 'Option O',
  'widgets.orderDate': 'Order Date',
  'widgets.orderStatus': 'Order Status',
  'widgets.orders': 'Orders',
  'widgets.ourLocations': 'Our Locations',
  'widgets.ourMissions': 'Our Missions',
  'widgets.ourMotivation': 'Our Motivation',
  'widgets.ourVission': 'Our Vission',
  'widgets.outlineChip': 'Outline Chip',
  'widgets.overallTrafficStatus': 'Traffic',
  'widgets.overlayCard': 'Overlay Card',
  'widgets.paid': 'Paid',
  'widgets.paper': 'Paper',
  'widgets.password': 'Password',
  'widgets.passwordPromptAlert': 'Password Prompt Alert',
  'widgets.pending': 'Pending',
  'widgets.permanentDrawers': 'Permanent Drawers',
  'widgets.permanentdrawer': 'Permanent Drawer',
  'widgets.persistentdrawer': 'Persistent Drawer',
  'widgets.personalDetails': 'Personal Details',
  'widgets.personalEdition': 'Personal Edition',
  'widgets.personalSchedule': 'My Schedule',
  'widgets.phoneNo': 'Phone',
  'widgets.pieChart': 'Pie Chart',
  'widgets.pinedSubHeader': 'Pined Sub Header',
  'widgets.pinnedSubheaderList': 'Pinned Subheader List',
  'widgets.pixelScript': 'QIPlus Pixel Script',
  'widgets.plan': 'Plan',
  'widgets.plans': 'Plans',
  'widgets.polarChart': 'Polar Chart',
  'widgets.positionedSnackbar': 'Positioned Snackbar',
  'widgets.positionedToolTips': 'Positioned Tooltips',
  'widgets.preventScrolledButtons': 'Prevent Scrolled Buttons',
  'widgets.preview': 'Preview',
  'widgets.previousChat': 'Previous Chat',
  'widgets.price': 'Price',
  'widgets.pricing': 'Pricing',
  'widgets.primary': 'Primary',
  'widgets.private': 'Private',
  'widgets.pro': 'Pro',
  'widgets.productReports': 'Product Reports',
  'widgets.productStats': 'Product Statistics',
  'widgets.productsReports': 'Products Reports',
  'widgets.professional': 'Professional',
  'widgets.professionals': 'Professionals',
  'widgets.profile': 'Profile',
  'widgets.projectManagement': 'Project Management',
  'widgets.projectTaskManagement': 'Project Task Management',
  'widgets.promptAlert': 'Prompt Alert',
  'widgets.qiplusPlan': 'QIPlus Plan',
  'widgets.qiplusUrl': 'QIPlus URL',
  'widgets.query': 'Query',
  'widgets.quillEditor': 'Quill Editor',
  'widgets.quoteOfTheDay': 'Quote Of The Day',
  'widgets.radarChart': 'Radar Chart',
  'widgets.radioButtons': 'Radio Buttons',
  'widgets.raisedButton': 'Raised Button',
  'widgets.ratings': 'Ratings',
  'widgets.reactAutoSuggest': 'React Auto Suggest',
  'widgets.reactAutoSuggests': 'React Auto Suggests',
  'widgets.reactButton': 'React Button',
  'widgets.reactDND': 'React DND',
  'widgets.reactGridControlledStateMode': 'React Grid Controlled State Mode',
  'widgets.reactSelect': 'React Select',
  'widgets.recentActivities': 'Recent Activity',
  'widgets.recentChat': 'Recent Chat',
  'widgets.recentNotifications': 'Recent Notifications',
  'widgets.recents': 'Recents',
  'widgets.refunded': 'Refunded',
  'widgets.reject': 'Discard',
  'widgets.responsiveFlipTable': 'Responsive Flip Table',
  'widgets.responsiveFullScreen': 'Responsive Full Screen',
  'widgets.responsiveTable': 'Responsive Table',
  'widgets.sales': 'Sales',
  'widgets.saveAsDrafts': 'Save As Drafts',
  'widgets.search': 'Search',
  'widgets.searchIdeas': 'Search Ideas',
  'widgets.searchInMailbox': 'Search in Emails',
  'widgets.searchMailList': 'Search in Emails',
  'widgets.secondaryHeadingAndColumns': 'Secondary Heading And Columns',
  'widgets.selectADefaultAddress': 'Select A Default Address',
  'widgets.selectMultiple': 'Select Multiple',
  'widgets.selectProject': 'Select Project',
  'widgets.selectTripDestination': 'Select Trip Destination',
  'widgets.selectableCalender': 'Selectable Calender',
  'widgets.selectedMenu': 'Selected Menu',
  'widgets.send': 'Send',
  'widgets.shipTo': 'Ship To',
  'widgets.shortDescription': 'Short Description',
  'widgets.signIn': 'Sign In',
  'widgets.signUp': 'Sign Up',
  'widgets.simpleAppBar': 'Simple App Bar',
  'widgets.simpleCards': 'Simple Cards',
  'widgets.simpleCheckbox': 'Simple Checkbox',
  'widgets.simpleDialogs': 'Simple Dialogs',
  'widgets.simpleExpansionPanel': 'Simple Expansion Panel',
  'widgets.simpleLists': 'Simple Lists',
  'widgets.simpleMenus': 'Simple Menus',
  'widgets.simpleSelect': 'Simple Select',
  'widgets.simpleSnackbar': 'Simple Snackbar',
  'widgets.simpleTextField': 'Simple Text Field',
  'widgets.singleLineGridLists': 'Single Line Grid Lists',
  'widgets.singleLineItem': 'Single Line Item',
  'widgets.siteVisitors': 'Visitors',
  'widgets.social': 'Social',
  'widgets.socialCompanines': 'Social Companines',
  'widgets.socialMediaButton': 'Social Media Button',
  'widgets.socialNewtork': 'Social Network',
  'widgets.socialNewtorks': 'Social Networks',
  'widgets.spam': 'Spam',
  'widgets.speacialTitleTreatment': 'Speacial Title Treatment',
  'widgets.stackedAreaChart': 'Stacked Area Chart',
  'widgets.stackedBarChart': 'Stacked Bar Chart',
  'widgets.standard': 'Standard',
  'widgets.starred': 'Starred',
  'widgets.startDate': 'Start Date',
  'widgets.startToBasic': 'Start with Basic',
  'widgets.status': 'Status',
  'widgets.stepper': 'Stepper',
  'widgets.stockExchange': 'Stock Exchange',
  'widgets.styles': 'Styles',
  'widgets.subject': 'Subject',
  'widgets.successAlert': 'Operation completed successfully',
  'widgets.support': 'Support',
  'widgets.supportRequest': 'Support Request',
  'widgets.swiches': 'Swiches',
  'widgets.switchLists': 'Switch Lists',
  'widgets.switches': 'Swiches',
  'widgets.tabs': 'Tabs',
  'widgets.target': 'Target',
  'widgets.taskList': 'Tasks',
  'widgets.tax': 'Tax',
  'widgets.team': 'Team',
  'widgets.teamEdition': 'Team Edition',
  'widgets.temporaryDrawers': 'Temporary Drawers',
  'widgets.text': 'Text',
  'widgets.textArea': 'Text Area',
  'widgets.thisWeek': 'This Week',
  'widgets.time': 'Time',
  'widgets.timePicker': 'Time Picker',
  'widgets.to': 'To',
  'widgets.toDoList': 'To-Do List',
  'widgets.today': 'Today',
  'widgets.todayOrders': "Today's Orders",
  'widgets.tooltip': 'ToolTip',
  'widgets.topSellings': 'Top Sellings',
  'widgets.total': 'Total',
  'widgets.totalActiveUsers': 'Total active users',
  'widgets.totalOrders': 'Total Orders',
  'widgets.totalRequest': 'Total Request',
  'widgets.totalRevenue': 'Total Revenue',
  'widgets.totalSales': 'Total Sales',
  'widgets.totalVisitors': 'Total Visitors',
  'widgets.total_over_range': 'Total Over Range',
  'widgets.trackingNumber': 'Tracking Number',
  'widgets.trafficChannel': 'Traffic Channel',
  'widgets.trafficSources': 'Traffic Sources',
  'widgets.transactionList': 'Transaction List',
  'widgets.transferReport': 'Transfer Report',
  'widgets.transitionControlDirection': 'Transition Control Direction',
  'widgets.tutorials': 'Tutorials',
  'widgets.tweets': 'Tweets',
  'widgets.typeYourQuestions': 'Type Your Questions',
  'widgets.uncontrolledDisableAlerts': 'Uncontrolled Disable Alerts',
  'widgets.unitPrice': 'Unit Price',
  'widgets.unset': 'Unset',
  'widgets.unsubscribe': 'Unsubscribe',
  'widgets.upcomingEvents': 'Upcoming Events',
  'widgets.updateProfile': 'Update Profile',
  'widgets.updateYourEmailAddress': 'Update Your Email Address',
  'widgets.updated10Minago': 'Updated 10 min ago',
  'widgets.upgrade': 'upgrade',
  'widgets.upgradePlan': 'Upgrade Plan',
  'widgets.upgradeToAdvance': 'Upgrade To Advance',
  'widgets.upgradeToEnableAction': 'Upgrade to enable this action',
  'widgets.upgradeToPro': 'Upgrade To Pro',
  'widgets.url': 'URL',
  'widgets.user': 'User',
  'widgets.username': 'Username',
  'widgets.usersList': 'Users List',
  'widgets.verticalChart': 'Vertical Chart',
  'widgets.verticalStepper': 'Vertical Stepper',
  'widgets.visitors': 'Visitors',
  'widgets.volume': 'Volume',
  'widgets.warningAlert': 'Warning Alert',
  'widgets.weekPicker': 'Week Picker',
  'widgets.weekly': 'Weekly',
  'widgets.widgets': 'Widgets',
  'widgets.withDisableTabs': 'With Disable Tabs',
  'widgets.withDownloadButton': 'With Download Button',
  'widgets.withError': 'With Error',
  'widgets.withHtmlAlert': 'With Html Alert',
  'widgets.wordpressTheme': 'Wordpress Theme',
  'widgets.workWeek': 'Work Week',
  'widgets.wrappedLabels': 'Wrapped Labels',
  'widgets.yearly': 'Yearly',
  'widgets.yesterday': 'Yesterday'
}

const pendingTranslationStrings = {
  'pending.translations': 'Should be here',
}

const moduleStrings = {}

Object.keys(AppModules).forEach(collection => {
  const thisModule = AppModules[collection]
  const menuGroup = thisModule.menu_group
  const collectionKeys = ['modules', 'sidebar', 'menu', 'triggers']

  let { label, singular, menu_label, shortname } = thisModule
  const menulabel = menu_label || label

  collectionKeys.forEach(key => {
    const thisLabel = key === 'menu' ? menulabel : label
    const singularLabel = singular || thisLabel
    const shortLabel = shortname || singularLabel

    moduleStrings[`${key}.${collection}`] = thisLabel
    moduleStrings[`${key}.${collection}.singular`] = singularLabel
    moduleStrings[`${key}.${collection}.short`] = shortLabel

    moduleStrings[`${key}.${collection}.added`] = `${singularLabel} Created`
    moduleStrings[`${key}.${collection}.changed`] = `${singularLabel} Modified`
    moduleStrings[`${key}.${collection}.removed`] = `${singularLabel} Removed`

    moduleStrings[`${key}.${collection}_added`] = moduleStrings[`${key}.${collection}.added`]
    moduleStrings[`${key}.${collection}_changed`] = moduleStrings[`${key}.${collection}.changed`]
    moduleStrings[`${key}.${collection}_removed`] = moduleStrings[`${key}.${collection}.removed`]
    if (menuGroup) {
      moduleStrings[`${key}.${menuGroup}Group`] = thisLabel

      if (!moduleStrings[`${key}.${menuGroup}`]) {
        moduleStrings[`${key}.${menuGroup}`] = thisLabel
        moduleStrings[`${key}.${menuGroup}.singular`] = singularLabel
        moduleStrings[`${key}.${menuGroup}.short`] = shortLabel
      }
    }
  })

  const editKeys = {
    view: 'See [%collection]',
    'view.singular': 'See [%singular]',
    new: 'New [%singular]',
    create: 'Create a [%singular]',
    add: 'Add [%singular]',
    createNew: 'Create new [%singular]',
    update: 'Update [%singular]',
    edit: 'Edit [%singular]',
    delete: 'Delete [%singular]',
    remove: 'Remove [%singular]',
    'clone.singular': 'Clone [%singular]',
    clone: 'Clone [%collection]',
    model: '[%singular] model',
    example: '[%singular] example',
    choose: 'Choose [%singular]',
    select: 'Select [%collection]',
    selected: '[%collection] selected',
    'selected.singular': '[%singular] selected',
    import: 'Import [%collection]',
    associate: 'Associate [%collection]',
    data: 'Data of [%singular]',
    confirmRemoval: 'Do you want to delete this [%singular]?',
    confirmUpdate: 'Do you want to update this [%singular]?',
    willBeSentToTrash: 'The [%singular] will be sent to trash',
    'willBeSentToTrash.plural': 'The [%collection] will be sent to trash',
    willBeRemoved: 'The [%singular] will be removed',
    'willBeRemoved.plural': 'The [%collection] will be removed',
    sentToTrash: '[%singular] sent to trash',
    'sentToTrash.plural': '[%collection] sent to trash',
    removed: 'The [%singular] will be removed',
    'removed.plural': 'The [%collection] will be removed',
    savedMsg: '[%singular] saved successfully',
    deletedMsg: '[%singular] deleted',
    'deletedMsg.plural': '[%collection] deleted',
    'deletedMsg.failure': '[%collection] could not be deleted',
    createdMsg: '[%singular] created successfully',
    'createdMsg.plural': '[%collection] created successfully',
    'createdMsg.failure': '[%collection] could not be created',
    updatedMsg: '[%singular] updated',
    'updatedMsg.plural': '[%collection] updated',
    'updatedMsg.failure': '[%collection] could not be updated',
    inexistsMsg: 'The [%singular] does not exist in the database',
    noPostsFound: 'No [%singular] found',
    savePostBefore: 'Save the [%singular] before continuing',
    leaveBlankForUnlimited: 'Leave blank to make all [%collection] from the account available',
  }

  Object.keys(editKeys).forEach(key => {
    let msg = editKeys[key]

    // English doesn't use grammatical gender
    const singularLabel = singular || label
    const shortLabel = shortname || singularLabel

    msg = msg.replace(/\[%collection\]/g, label)
    msg = msg.replace(/\[%singular\]/g, singularLabel)
    msg = msg.replace(/\[%shortname\]/g, shortLabel)
    // Remove gender-related placeholders for English
    msg = msg.replace(/\[%gender\]/g, '')
    msg = msg.replace(/\[%fem\]/g, '')
    msg = msg.replace(/\[%masc\]/g, '')

    moduleStrings[`${collection}.${key}`] = msg
    moduleStrings[`${collection}.${key}.short`] = msg.replace(singularLabel, shortLabel)
  })
})

Object.keys(AppTaxonomies).forEach(taxName => {
  const thisTaxonomy = AppTaxonomies[taxName]
  const menuGroup = thisTaxonomy.menu_group
  const collectionKeys = ['taxonomies', 'sidebar', 'menu']

  let { label, singular, menu_label } = thisTaxonomy
  const menulabel = menu_label || label

  const editKeys = {
    view: 'View [%taxName]',
    'view.singular': 'View [%singular]',
    new: 'New [%singular]',
    create: 'Create [%singular]',
    add: 'Add [%singular]',
    createNew: 'Create new [%singular]',
    update: 'Update [%singular]',
    edit: 'Edit [%singular]',
    delete: 'Delete [%singular]',
    remove: 'Remove [%singular]',
    'clone.singular': 'Clone [%singular]',
    clone: 'Clone [%taxName]',
    model: '[%singular] model',
    example: '[%singular] example',
    choose: 'Choose [%taxName]',
    select: 'Select [%taxName]',
    associate: 'Associate [%taxName]',
    data: 'Data of [%singular]',
    confirmRemoval: 'Do you want to delete this [%singular]?',
    confirmUpdate: 'Do you want to update this [%singular]?',
    willBeSentToTrash: 'The [%singular] will be sent to trash',
    willBeRemoved: 'The [%singular] will be removed',
    deletedMsg: '[%singular] deleted',
    'deletedMsg.plural': '[%collection] deleted',
    'deletedMsg.failure': '[%collection] could not be deleted',
    createdMsg: '[%singular] created successfully',
    'createdMsg.plural': '[%collection] created successfully',
    'createdMsg.failure': '[%collection] could not be created',
    updatedMsg: '[%singular] updated',
    'updatedMsg.plural': '[%collection] updated',
    'updatedMsg.failure': '[%collection] could not be updated',
    inexistsMsg: 'The [%singular] does not exist in the database',
    noPostsFound: 'No [%singular] found',
  }

  thisTaxonomy.collections.forEach(collection => {
    const thisModule = AppModules[collection]

    collectionKeys.forEach(key => {
      const thisLabel = key === 'menu' ? menulabel : label
      const singularLabel = singular || thisLabel

      moduleStrings[`${key}.${taxName}.${collection}`] = thisLabel
      moduleStrings[`${key}.${taxName}.${collection}.singular`] = singularLabel

      if (menuGroup) {
        moduleStrings[`${key}.${menuGroup}Group`] = thisLabel

        if (!moduleStrings[`${key}.${menuGroup}`]) {
          moduleStrings[`${key}.${menuGroup}`] = thisLabel
          moduleStrings[`${key}.${menuGroup}.singular`] = singularLabel
        }
      }
    })

    Object.keys(editKeys).forEach(key => {
      let msg = editKeys[key]

      // English doesn't use grammatical gender
      const singularLabel = singular || label

      msg = msg.replace(/\[%taxName\]/g, label)
      msg = msg.replace(/\[%collection\]/g, thisModule.label)
      msg = msg.replace(/\[%singular\]/g, singularLabel)
      // Remove gender-related placeholders for English
      msg = msg.replace(/\[%gender\]/g, '')
      msg = msg.replace(/\[%fem\]/g, '')
      msg = msg.replace(/\[%masc\]/g, '')

      moduleStrings[`${taxName}.${collection}.${key}`] = msg

      if (!moduleStrings[`${taxName}.${key}`]) {
        moduleStrings[`${taxName}.${key}`] = msg
      }
    })
  })
})

const langMessages = {
  ...en_US_Strings,
  ...moduleStrings,
  ...pendingTranslationStrings,
}
module.exports = langMessages
