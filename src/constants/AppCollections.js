/* eslint-disable indent */
/**
 * App Collections
 *
 * @note
 * This file should be synced between the qiplus-frontend and the qiplus-firebase repos.
 *
 * Before making any changes to this file, please make sure the workspace.js script is
 * running on your environment.
 *
 * qiplus-frontend/src/constants/AppCollections.js > qiplus-firebase/functions/constants/collections.js
 */
const COUNTER_ID = '_count'
const INDEX_ID = '_index'

const COMMISSIONS_SUBCOLLECTION_NAME = 'commissions'
const INVOICES_SUBCOLLECTION_NAME = 'invoices'
const KEYWORDS_SUBCOLLECTION_NAME = 'keywords'
const MESSAGES_SUBCOLLECTION_NAME = 'messages'
const SCORES_SUBCOLLECTION_NAME = 'scores'
const UNSUBSCRIBES_SUBCOLLECTION_NAME = 'unsubscribes'
const UPDATES_SUBCOLLECTION_NAME = 'updates'

const CHATS_COLLECTION_NAME = 'chats'
const CRONJOBS_COLLECTION_NAME = 'cronjobs'
const LISTENERS_COLLECTION_NAME = 'listeners'
const MAIL_COLLECTION_NAME = 'emails'
const MAILBOXES_COLLECTION_NAME = 'mailboxes'
const TRASH_COLLECTION_NAME = 'trash'

const ACCOUNTS_COLLECTION_NAME = 'accounts'
const AFFILIATES_COLLECTION_NAME = 'affiliates'
const AUTOMATIONS_COLLECTION_NAME = 'automations'
const BARCODE_PRODUCTS_COLLECTION_NAME = 'barcode-products'
const BROADCASTS_COLLECTION_NAME = 'broadcasts'
const CALENDAR_COLLECTION_NAME = 'calendar'
const CAMPAIGNS_COLLECTION_NAME = 'campaigns'
const CONTACTS_SUBCOLLECTION_NAME = 'contacts'
const CONTRACTS_COLLECTION_NAME = 'contracts'
const DEALS_COLLECTION_NAME = 'deals'
const DESKTOP_NOTIFICATIONS_COLLECTION_NAME = 'desktop-notifications'
const EVENTS_COLLECTION_NAME = 'events'
const FEATURES_PLANS_COLLECTION_NAME = 'features-plans'
const FIELDS_COLLECTION_NAME = 'fields'
const FORMS_COLLECTION_NAME = 'forms'
const FUNNELS_COLLECTION_NAME = 'funnels'
const HOT_POSTS_COLLECTION_NAME = 'hot-posts'
const INTEGRATIONS_COLLECTION_NAME = 'integrations'
const INSTANCES_COLLECTION_NAME = 'instances'
const LANDING_PAGES_COLLECTION_NAME = 'landing-pages'
const LEADS_COLLECTION_NAME = 'leads'
const LIVE_POSTS_COLLECTION_NAME = 'live-posts'
const LIVE_QIPLUS_COLLECTION_NAME = 'live-qiplus'
const MAILING_COLLECTION_NAME = 'mailing'
const NOTIFICATIONS_COLLECTION_NAME = 'notifications'
const PACKS_COLLECTION_NAME = 'packs'
const PRIZES_COLLECTION_NAME = 'prizes'
const TREEBUSINESS_COLLECTION_NAME = 'treebusiness'
const PRODUCTS_COLLECTION_NAME = 'products'
const QIPLUS_PLANS_COLLECTION_NAME = 'qiplus-plans'
const QIUSERS_COLLECTION_NAME = 'qiusers'
const QUESTIONNAIRES_COLLECTION_NAME = 'questionnaires'
const RAFFLES_COLLECTION_NAME = 'raffles'
const SEGMENTATIONS_COLLECTION_NAME = 'segmentations'
const SHOTX_COLLECTION_NAME = 'shotx'
const SHOTX_CRON_COLLECTION_NAME = 'shotx-cron'
const SHOTX_INSTANCES_COLLECTION_NAME = 'instances'
const SHOTX_QUICK_MESSAGES_COLLECTION_NAME = 'quick_messages'
const SHOTX_INTERACTIONS_COLLECTION_NAME = 'interactions'
const SHOTX_SNIPERS_COLLECTION_NAME = 'snipers'
const STORES_COLLECTION_NAME = 'stores'
const TASKLISTS_COLLECTION_NAME = 'tasklists'
const TEAMS_COLLECTION_NAME = 'teams'
const TEMPLATES_COLLECTION_NAME = 'templates'
const TICKETS_COLLECTION_NAME = 'tickets'
const TRACKINGS_COLLECTION_NAME = 'trackings'
const INSTANCES_CHATS_COLLECTION_NAME = 'instances'
const HISTORIC_COLLECTION_NAME = 'historic'
const STORIES_SUBCOLLECTION_NAME = 'stories'
const OTHERS_COLLECTION_NAME = 'others'
const LOCALE_COLLECTION_NAME = 'locale'
const COUNTERS_COLLECTION_NAME = 'counters'
const HUNTERS_COLLECTION_NAME = 'hunters'
const SUBSCRIPTIONS_COLLECTION_NAME = 'subscriptions'
const WARNINGS_COLLECTION_NAME = 'warnings'

const adminCollections = [
  ACCOUNTS_COLLECTION_NAME,
  FEATURES_PLANS_COLLECTION_NAME,
  QIPLUS_PLANS_COLLECTION_NAME,
]

const moduledCollections = [
  AUTOMATIONS_COLLECTION_NAME,
  CALENDAR_COLLECTION_NAME,
  CAMPAIGNS_COLLECTION_NAME,
  CHATS_COLLECTION_NAME,
  CONTRACTS_COLLECTION_NAME,
  DEALS_COLLECTION_NAME,
  EVENTS_COLLECTION_NAME,
  FIELDS_COLLECTION_NAME,
  FORMS_COLLECTION_NAME,
  FUNNELS_COLLECTION_NAME,
  INTEGRATIONS_COLLECTION_NAME,
  LANDING_PAGES_COLLECTION_NAME,
  LEADS_COLLECTION_NAME,
  MAIL_COLLECTION_NAME,
  MAILBOXES_COLLECTION_NAME,
  MAILING_COLLECTION_NAME,
  NOTIFICATIONS_COLLECTION_NAME,
  PRIZES_COLLECTION_NAME,
  PRODUCTS_COLLECTION_NAME,
  QIUSERS_COLLECTION_NAME,
  QUESTIONNAIRES_COLLECTION_NAME,
  SEGMENTATIONS_COLLECTION_NAME,
  SHOTX_COLLECTION_NAME,
  HUNTERS_COLLECTION_NAME,
  STORES_COLLECTION_NAME,
  TASKLISTS_COLLECTION_NAME,
  TEAMS_COLLECTION_NAME,
  TICKETS_COLLECTION_NAME,
  TRACKINGS_COLLECTION_NAME,
  SHOTX_INTERACTIONS_COLLECTION_NAME,
  SHOTX_SNIPERS_COLLECTION_NAME,
  SUBSCRIPTIONS_COLLECTION_NAME,
]

const coreCollections = [
  ACCOUNTS_COLLECTION_NAME,
  AFFILIATES_COLLECTION_NAME,
  AUTOMATIONS_COLLECTION_NAME,
  BARCODE_PRODUCTS_COLLECTION_NAME,
  BROADCASTS_COLLECTION_NAME,
  CALENDAR_COLLECTION_NAME,
  CAMPAIGNS_COLLECTION_NAME,
  CHATS_COLLECTION_NAME,
  CONTRACTS_COLLECTION_NAME,
  DEALS_COLLECTION_NAME,
  DESKTOP_NOTIFICATIONS_COLLECTION_NAME,
  EVENTS_COLLECTION_NAME,
  FEATURES_PLANS_COLLECTION_NAME,
  FIELDS_COLLECTION_NAME,
  FORMS_COLLECTION_NAME,
  FUNNELS_COLLECTION_NAME,
  HOT_POSTS_COLLECTION_NAME,
  INTEGRATIONS_COLLECTION_NAME,
  LANDING_PAGES_COLLECTION_NAME,
  LEADS_COLLECTION_NAME,
  LIVE_POSTS_COLLECTION_NAME,
  LIVE_QIPLUS_COLLECTION_NAME,
  MAILBOXES_COLLECTION_NAME,
  MAILING_COLLECTION_NAME,
  NOTIFICATIONS_COLLECTION_NAME,
  PACKS_COLLECTION_NAME,
  PRIZES_COLLECTION_NAME,
  PRODUCTS_COLLECTION_NAME,
  QIPLUS_PLANS_COLLECTION_NAME,
  QIUSERS_COLLECTION_NAME,
  QUESTIONNAIRES_COLLECTION_NAME,
  RAFFLES_COLLECTION_NAME,
  SEGMENTATIONS_COLLECTION_NAME,
  SHOTX_COLLECTION_NAME,
  HUNTERS_COLLECTION_NAME,
  STORES_COLLECTION_NAME,
  TASKLISTS_COLLECTION_NAME,
  TEAMS_COLLECTION_NAME,
  TEMPLATES_COLLECTION_NAME,
  TICKETS_COLLECTION_NAME,
  TRACKINGS_COLLECTION_NAME,
  SHOTX_INTERACTIONS_COLLECTION_NAME,
  SHOTX_SNIPERS_COLLECTION_NAME,
  SUBSCRIPTIONS_COLLECTION_NAME,
]

const KEYWORDS_SUBCOLLECTIONS_MAP = {}

const keywordsSubcollections = [
  DEALS_COLLECTION_NAME, // deals
  LEADS_COLLECTION_NAME, // leads
]

coreCollections.forEach(c => {
  KEYWORDS_SUBCOLLECTIONS_MAP[c] = KEYWORDS_SUBCOLLECTION_NAME
})

keywordsSubcollections.forEach(c => {
  KEYWORDS_SUBCOLLECTIONS_MAP[c] = `${c}_${KEYWORDS_SUBCOLLECTION_NAME}`
})

const KEYWORDS_FIELDS_MAP = {}

coreCollections.forEach(collection => {
  let keywordFields = [],
    substrFields = [],
    limitedFields = [],
    joinFields = []

  switch (collection) {
    case DEALS_COLLECTION_NAME:
      keywordFields = [
        'title',
        'email',
        'mobile',
        'firstName',
        'lastName',
        'displayName',
        'companyName',
        'cpf',
        'cnpj',
      ]
      substrFields = [
        'title',
        'email',
        'firstName',
        'lastName',
        'displayName',
        'companyName',
      ]
      limitedFields = ['gender', 'type']
      joinFields = ['funnel']

      break
    case LEADS_COLLECTION_NAME:
      keywordFields = [
        'email',
        'firstName',
        'lastName',
        'displayName',
        'companyName',
        'cpf',
        'cnpj',
        'mobile',
        'phone',
        'gender',
        'occupation',
      ]
      substrFields = [
        'email',
        'firstName',
        'lastName',
        'displayName',
        'companyName',
      ]
      limitedFields = ['gender', 'type']
      joinFields = []

      break
    case QIUSERS_COLLECTION_NAME:
    case AFFILIATES_COLLECTION_NAME:
      keywordFields = [
        'email',
        'firstName',
        'lastName',
        'displayName',
        'companyName',
        'cpf',
        'cnpj',
        'mobile',
        'phone',
        'gender',
        'occupation',
      ]
      substrFields = [
        'email',
        'firstName',
        'lastName',
        'displayName',
        'companyName',
      ]
      limitedFields = ['gender', 'type']
      joinFields = []

      break
    case MAILBOXES_COLLECTION_NAME:
      keywordFields = ['title', 'from', 'fromName', 'provider', 'forward']
      substrFields = keywordFields
      limitedFields = []
      joinFields = []

      break
    case CHATS_COLLECTION_NAME:
      keywordFields = [
        'firstName',
        'lastName',
        'groupName',
        'subject',
        'description',
      ]
      substrFields = keywordFields
      limitedFields = []
      joinFields = []

      break
    default:
      keywordFields = ['title']
      substrFields = ['title']
      limitedFields = []
      joinFields = []

      break
  }

  const allKeywordFields = [...keywordFields, 'description']
  const inheritedFields = [
    'createdAt',
    'updatedAt',
    'locale',
    'collection',
    'tags',
  ]

  KEYWORDS_FIELDS_MAP[collection] = {
    allKeywordFields,
    keywordFields,
    substrFields,
    inheritedFields,
    limitedFields,
    joinFields,
  }
})

const CHECKIN_COLLECTION_NAME = 'checkin'
const PARTICIPANTS_COLLECTION_NAME = 'participants'

const relationalCollections = [
  CHECKIN_COLLECTION_NAME, // events -> leads [true/false]
  PARTICIPANTS_COLLECTION_NAME, // events -> leads [true/false]
]

const LOGS_COLLECTION_NAME = 'logs'
const INTERNAL_LOGS_COLLECTION_NAME = 'internalLogs'
const SESSIONS_COLLECTION_NAME = 'sessions'

const logsCollections = [
  LOGS_COLLECTION_NAME, // logs -> collection [array]
  INTERNAL_LOGS_COLLECTION_NAME, // logs -> collection [array]
]

const SHORT_URL_COLLECTIONS_PARAMS = {
  [LANDING_PAGES_COLLECTION_NAME]: 'l',
  [FORMS_COLLECTION_NAME]: 'f',
  [CAMPAIGNS_COLLECTION_NAME]: 'c',
}

const SHORT_URL_COLLECTIONS_PARAMS_MAP = {}
Object.keys(SHORT_URL_COLLECTIONS_PARAMS).forEach(c => {
  let k = SHORT_URL_COLLECTIONS_PARAMS[c]
  SHORT_URL_COLLECTIONS_PARAMS_MAP[k] = c
})

const TAXONOMIES_COLLECTION_NAME = 'taxonomies'
const EXCHANGE_MODE_TAXONOMY_NAME = 'exchange_mode'
const CATEGORIES_TAXONOMY_NAME = 'categories'
const TAGS_TAXONOMY_NAME = 'tags'
const USERS_PROFILES_TAXONOMY_NAME = 'users_profiles'
const GATEWAY_TAXONOMY_NAME = 'gateway'

const taxonomies = [
  EXCHANGE_MODE_TAXONOMY_NAME,
  CATEGORIES_TAXONOMY_NAME,
  TAGS_TAXONOMY_NAME,
  USERS_PROFILES_TAXONOMY_NAME,
  GATEWAY_TAXONOMY_NAME,
]

const taxonomiesRelations = {
  [EXCHANGE_MODE_TAXONOMY_NAME]: [PRODUCTS_COLLECTION_NAME],
  [CATEGORIES_TAXONOMY_NAME]: [
    PRODUCTS_COLLECTION_NAME /* PACKS_COLLECTION_NAME */,
  ],
  [TAGS_TAXONOMY_NAME]: [
    PRODUCTS_COLLECTION_NAME,
    LEADS_COLLECTION_NAME,
    DEALS_COLLECTION_NAME,
    MAIL_COLLECTION_NAME,
    CHATS_COLLECTION_NAME,
  ],
  [USERS_PROFILES_TAXONOMY_NAME]: [LEADS_COLLECTION_NAME],
  [GATEWAY_TAXONOMY_NAME]: [TICKETS_COLLECTION_NAME],
}

const COLLECTION_TRIGGERS = {}
coreCollections.forEach(collection => {
  COLLECTION_TRIGGERS[collection] = {
    added: `${collection}_added`,
    changed: `${collection}_changed`,
    removed: `${collection}_removed`,
  }
})

const FIRESTORE_INDEXES = {
  simple: {
    [LOGS_COLLECTION_NAME]: {
      seller: 'data.seller',
      sellers: 'data.seller',
      manager: 'data.manager',
      managers: 'data.manager',
      team: 'data.team',
      teams: 'data.team',
    },
  },
}

const COLLECTIONS = {
  ACCOUNTS_COLLECTION_NAME,
  adminCollections,
  AFFILIATES_COLLECTION_NAME,
  AUTOMATIONS_COLLECTION_NAME,
  BROADCASTS_COLLECTION_NAME,
  BARCODE_PRODUCTS_COLLECTION_NAME,
  CALENDAR_COLLECTION_NAME,
  CAMPAIGNS_COLLECTION_NAME,
  CATEGORIES_TAXONOMY_NAME,
  CHATS_COLLECTION_NAME,
  CHECKIN_COLLECTION_NAME,
  COLLECTION_TRIGGERS,
  COMMISSIONS_SUBCOLLECTION_NAME,
  CONTACTS_SUBCOLLECTION_NAME,
  CONTRACTS_COLLECTION_NAME,
  coreCollections,
  COUNTER_ID,
  CRONJOBS_COLLECTION_NAME,
  DEALS_COLLECTION_NAME,
  DESKTOP_NOTIFICATIONS_COLLECTION_NAME,
  EVENTS_COLLECTION_NAME,
  EXCHANGE_MODE_TAXONOMY_NAME,
  FEATURES_PLANS_COLLECTION_NAME,
  FIELDS_COLLECTION_NAME,
  FIRESTORE_INDEXES,
  FORMS_COLLECTION_NAME,
  FUNNELS_COLLECTION_NAME,
  GATEWAY_TAXONOMY_NAME,
  HOT_POSTS_COLLECTION_NAME,
  INDEX_ID,
  INTEGRATIONS_COLLECTION_NAME,
  INSTANCES_COLLECTION_NAME,
  INTERNAL_LOGS_COLLECTION_NAME,
  INVOICES_SUBCOLLECTION_NAME,
  KEYWORDS_FIELDS_MAP,
  KEYWORDS_SUBCOLLECTION_NAME,
  KEYWORDS_SUBCOLLECTIONS_MAP,
  keywordsSubcollections,
  LANDING_PAGES_COLLECTION_NAME,
  LEADS_COLLECTION_NAME,
  LISTENERS_COLLECTION_NAME,
  LIVE_POSTS_COLLECTION_NAME,
  LIVE_QIPLUS_COLLECTION_NAME,
  LOGS_COLLECTION_NAME,
  logsCollections,
  MAIL_COLLECTION_NAME,
  MAILBOXES_COLLECTION_NAME,
  MAILING_COLLECTION_NAME,
  MESSAGES_SUBCOLLECTION_NAME,
  moduledCollections,
  NOTIFICATIONS_COLLECTION_NAME,
  PACKS_COLLECTION_NAME,
  PARTICIPANTS_COLLECTION_NAME,
  PRIZES_COLLECTION_NAME,
  PRODUCTS_COLLECTION_NAME,
  QIPLUS_PLANS_COLLECTION_NAME,
  QIUSERS_COLLECTION_NAME,
  QUESTIONNAIRES_COLLECTION_NAME,
  RAFFLES_COLLECTION_NAME,
  relationalCollections,
  SCORES_SUBCOLLECTION_NAME,
  SEGMENTATIONS_COLLECTION_NAME,
  SESSIONS_COLLECTION_NAME,
  SHORT_URL_COLLECTIONS_PARAMS_MAP,
  SHORT_URL_COLLECTIONS_PARAMS,
  SHOTX_COLLECTION_NAME,
  HUNTERS_COLLECTION_NAME,
  SHOTX_CRON_COLLECTION_NAME,
  OTHERS_COLLECTION_NAME,
  LOCALE_COLLECTION_NAME,
  SHOTX_QUICK_MESSAGES_COLLECTION_NAME,
  SHOTX_INTERACTIONS_COLLECTION_NAME,
  SHOTX_SNIPERS_COLLECTION_NAME,
  SHOTX_INSTANCES_COLLECTION_NAME,
  STORES_COLLECTION_NAME,
  TAGS_TAXONOMY_NAME,
  TASKLISTS_COLLECTION_NAME,
  TAXONOMIES_COLLECTION_NAME,
  taxonomies,
  taxonomiesRelations,
  TEAMS_COLLECTION_NAME,
  TEMPLATES_COLLECTION_NAME,
  TICKETS_COLLECTION_NAME,
  TRACKINGS_COLLECTION_NAME,
  TRASH_COLLECTION_NAME,
  UNSUBSCRIBES_SUBCOLLECTION_NAME,
  UPDATES_SUBCOLLECTION_NAME,
  USERS_PROFILES_TAXONOMY_NAME,
  INSTANCES_CHATS_COLLECTION_NAME,
  HISTORIC_COLLECTION_NAME,
  STORIES_SUBCOLLECTION_NAME,
  TREEBUSINESS_COLLECTION_NAME,
  COUNTERS_COLLECTION_NAME,
  SUBSCRIPTIONS_COLLECTION_NAME,
  WARNINGS_COLLECTION_NAME
}

module.exports = COLLECTIONS
