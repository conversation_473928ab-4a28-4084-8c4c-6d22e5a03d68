import { makeStyles } from '@material-ui/core/styles';

import React from 'react';

const useStyles = makeStyles((theme) => ({
    root: {
        position: 'relative',
        width: 40,
        height: 40,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: '50%',
        transition: 'all 0.3s ease',
        '&::before': {
            content: '""',
            position: 'absolute',
            width: 8,
            height: 8,
            bottom: -4,
            left: 14,
            transform: 'rotate(45deg)',
        },
    },
    inner: {
        width: 32,
        height: 32,
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'all 0.3s ease',
    },
}));


export function GradientIcon({ children, className }) {
    const classes = useStyles();
    const isDarkMode = Boolean(localStorage.getItem('config.darkMode'));

    return (
        <div className={classes.root}
            style={isDarkMode ? {
                backgroundColor: '#0d1422',
                '&::before': {
                    backgroundColor: '#0d1422',
                }

            } : {
                backgroundColor: '#f8fafc',
                '&::before': {
                    backgroundColor: '#f8fafc',
                }
            }}
        >
            <div className={classes.inner}
                style={isDarkMode ? {
                    background: 'linear-gradient(135deg, #1a1f2c 0%, #2d3748 100%)',
                } : {
                    background: 'linear-gradient(135deg, #e2e8f0 0%, #f1f5f9 100%)',
                }}
            >
                {children}
            </div>
        </div >
    );
}
