import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { createStore } from 'redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';

// Mock do componente principal (simplificado para teste)
const MockBroadcastPage = () => {
  const [instances] = React.useState([
    {
      id: '1',
      title: 'WhatsApp Instance 1',
      platform: 'whatsapp',
      status: { state: 'open' }
    },
    {
      id: '2',
      title: 'Instagram Instance 1',
      platform: 'instagram',
      status: { state: 'open' }
    }
  ]);

  const [selectedInstances, setSelectedInstances] = React.useState([]);
  const [multipleInstanceMode, setMultipleInstanceMode] = React.useState(false);
  const [leads, setLeads] = React.useState([]);

  const handleInstancesChange = (instanceIds) => {
    setSelectedInstances(instanceIds);
    // Simular carregamento de leads
    if (instanceIds.length > 0) {
      const mockLeads = instanceIds.map(id => ({
        id: `lead-${id}`,
        displayName: `Lead from Instance ${id}`,
        mobile: '11999999999',
        mobileCC: '55'
      }));
      setLeads(mockLeads);
    } else {
      setLeads([]);
    }
  };

  const handleSelectAllInstances = () => {
    setSelectedInstances(instances.map(inst => inst.id));
  };

  const handleClearInstanceSelection = () => {
    setSelectedInstances([]);
    setLeads([]);
  };

  const toggleMultipleInstanceMode = () => {
    setMultipleInstanceMode(!multipleInstanceMode);
    setSelectedInstances([]);
    setLeads([]);
  };

  return (
    <div>
      <div>
        <label>
          <input
            type="checkbox"
            checked={multipleInstanceMode}
            onChange={toggleMultipleInstanceMode}
            data-testid="multiple-mode-toggle"
          />
          Seleção Múltipla
        </label>
      </div>

      {multipleInstanceMode ? (
        <div data-testid="multiple-selection-container">
          <button onClick={handleSelectAllInstances}>Selecionar Todas</button>
          <button onClick={handleClearInstanceSelection}>Limpar Seleção</button>
          
          {instances.map(instance => (
            <div key={instance.id}>
              <label>
                <input
                  type="checkbox"
                  checked={selectedInstances.includes(instance.id)}
                  onChange={() => {
                    const newSelection = selectedInstances.includes(instance.id)
                      ? selectedInstances.filter(id => id !== instance.id)
                      : [...selectedInstances, instance.id];
                    handleInstancesChange(newSelection);
                  }}
                  data-testid={`instance-checkbox-${instance.id}`}
                />
                {instance.title}
              </label>
            </div>
          ))}
          
          {selectedInstances.length > 0 && (
            <div data-testid="selected-instances-display">
              Instâncias Selecionadas: {selectedInstances.length}
            </div>
          )}
        </div>
      ) : (
        <div data-testid="single-selection-container">
          <select data-testid="single-instance-select">
            <option value="">Selecione uma instância</option>
            {instances.map(instance => (
              <option key={instance.id} value={instance.id}>
                {instance.title}
              </option>
            ))}
          </select>
        </div>
      )}

      <div data-testid="leads-container">
        <h3>Leads ({leads.length})</h3>
        {leads.map(lead => (
          <div key={lead.id} data-testid={`lead-${lead.id}`}>
            {lead.displayName}
          </div>
        ))}
      </div>
    </div>
  );
};

// Mock do Redux store
const mockStore = createStore(() => ({
  authReducer: {
    account: { id: 'test-account' },
    user: { id: 'test-user' }
  }
}));

const renderWithProviders = (component) => {
  return render(
    <Provider store={mockStore}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('Integração de Seleção Múltipla de Instâncias', () => {
  test('deve alternar entre modo de seleção única e múltipla', () => {
    renderWithProviders(<MockBroadcastPage />);

    // Inicialmente deve estar em modo de seleção única
    expect(screen.getByTestId('single-selection-container')).toBeInTheDocument();
    expect(screen.queryByTestId('multiple-selection-container')).not.toBeInTheDocument();

    // Alternar para modo múltiplo
    fireEvent.click(screen.getByTestId('multiple-mode-toggle'));

    expect(screen.getByTestId('multiple-selection-container')).toBeInTheDocument();
    expect(screen.queryByTestId('single-selection-container')).not.toBeInTheDocument();
  });

  test('deve permitir seleção múltipla de instâncias', async () => {
    renderWithProviders(<MockBroadcastPage />);

    // Ativar modo múltiplo
    fireEvent.click(screen.getByTestId('multiple-mode-toggle'));

    // Selecionar primeira instância
    fireEvent.click(screen.getByTestId('instance-checkbox-1'));

    await waitFor(() => {
      expect(screen.getByTestId('selected-instances-display')).toHaveTextContent('Instâncias Selecionadas: 1');
      expect(screen.getByTestId('lead-lead-1')).toBeInTheDocument();
    });

    // Selecionar segunda instância
    fireEvent.click(screen.getByTestId('instance-checkbox-2'));

    await waitFor(() => {
      expect(screen.getByTestId('selected-instances-display')).toHaveTextContent('Instâncias Selecionadas: 2');
      expect(screen.getByTestId('lead-lead-2')).toBeInTheDocument();
    });
  });

  test('deve permitir selecionar todas as instâncias', async () => {
    renderWithProviders(<MockBroadcastPage />);

    // Ativar modo múltiplo
    fireEvent.click(screen.getByTestId('multiple-mode-toggle'));

    // Clicar em "Selecionar Todas"
    fireEvent.click(screen.getByText('Selecionar Todas'));

    await waitFor(() => {
      expect(screen.getByTestId('selected-instances-display')).toHaveTextContent('Instâncias Selecionadas: 2');
      expect(screen.getByTestId('instance-checkbox-1')).toBeChecked();
      expect(screen.getByTestId('instance-checkbox-2')).toBeChecked();
    });
  });

  test('deve permitir limpar seleção', async () => {
    renderWithProviders(<MockBroadcastPage />);

    // Ativar modo múltiplo e selecionar instâncias
    fireEvent.click(screen.getByTestId('multiple-mode-toggle'));
    fireEvent.click(screen.getByText('Selecionar Todas'));

    await waitFor(() => {
      expect(screen.getByTestId('selected-instances-display')).toBeInTheDocument();
    });

    // Limpar seleção
    fireEvent.click(screen.getByText('Limpar Seleção'));

    await waitFor(() => {
      expect(screen.queryByTestId('selected-instances-display')).not.toBeInTheDocument();
      expect(screen.getByTestId('instance-checkbox-1')).not.toBeChecked();
      expect(screen.getByTestId('instance-checkbox-2')).not.toBeChecked();
      expect(screen.getByTestId('leads-container')).toHaveTextContent('Leads (0)');
    });
  });

  test('deve carregar leads quando instâncias são selecionadas', async () => {
    renderWithProviders(<MockBroadcastPage />);

    // Ativar modo múltiplo
    fireEvent.click(screen.getByTestId('multiple-mode-toggle'));

    // Verificar que não há leads inicialmente
    expect(screen.getByTestId('leads-container')).toHaveTextContent('Leads (0)');

    // Selecionar uma instância
    fireEvent.click(screen.getByTestId('instance-checkbox-1'));

    await waitFor(() => {
      expect(screen.getByTestId('leads-container')).toHaveTextContent('Leads (1)');
      expect(screen.getByTestId('lead-lead-1')).toBeInTheDocument();
    });

    // Selecionar segunda instância
    fireEvent.click(screen.getByTestId('instance-checkbox-2'));

    await waitFor(() => {
      expect(screen.getByTestId('leads-container')).toHaveTextContent('Leads (2)');
      expect(screen.getByTestId('lead-lead-2')).toBeInTheDocument();
    });
  });
});
