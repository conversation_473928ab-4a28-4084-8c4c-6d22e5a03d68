import { Box, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { langMessages } from 'Lang/index';
import React from 'react';
import { Badge } from 'reactstrap';

const useStyles = makeStyles((theme) => ({
  root: {
    marginBottom: theme.spacing(2),
  },
  badge: {
    margin: theme.spacing(0.5),
    cursor: 'pointer',
    mr: 2,
  },
}));

const model = {
  tags: [],
  selectedTags: [],
  onTagSelect: (tag) => { },
}

const TagFilter = ({ tags, selectedTags, onTagSelect } = model) => {
  const classes = useStyles();

  return (
    <Box className={classes.root}>
      <Typography variant="subtitle2" gutterBottom>
        {langMessages['shotx.broadcast.tagsFilter']}:
      </Typography>
      {tags.map(({ id, title, color }) => (
        <Badge
          key={id}
          size="small"
          className={(classes.badge) + (selectedTags.includes(id) ? ` bg-${color || 'primary'}` : ' bg-default')}
          onClick={() => onTagSelect(id)}
        >
          {title}
        </Badge>
      ))}
    </Box>
  );
};

export default TagFilter;
