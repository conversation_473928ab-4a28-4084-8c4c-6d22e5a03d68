import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import InstanceAutocomplete from '../InstanceAutocomplete';

// Mock das traduções
jest.mock('Lang/index', () => ({
  langMessages: {
    'shotx.broadcast.instanceSelectLabel': 'Selecione ou busque uma instância',
    'shotx.broadcast.selectAll': 'Selecionar todos',
    'shotx.broadcast.selectNone': 'Limpar seleção',
    'shotx.broadcast.selectedInstances': 'Instâncias Selecionadas'
  }
}));

// Mock do componente InstanceSelectCard
jest.mock('../../../../components-new/molecules', () => ({
  InstanceSelectCard: ({ instance }) => (
    <div data-testid={`instance-card-${instance.id}`}>
      {instance.title} - {instance.platform}
    </div>
  )
}));

const mockInstances = [
  {
    id: '1',
    title: 'WhatsApp Instance 1',
    platform: 'whatsapp',
    status: { state: 'open' }
  },
  {
    id: '2',
    title: 'Instagram Instance 1',
    platform: 'instagram',
    status: { state: 'open' }
  },
  {
    id: '3',
    title: 'WhatsApp Instance 2',
    platform: 'whatsapp',
    status: { state: 'close' }
  }
];

describe('InstanceAutocomplete', () => {
  describe('Seleção Única', () => {
    const defaultProps = {
      instances: mockInstances,
      selectedInstanceId: '',
      onInstanceChange: jest.fn(),
      disabled: false,
      multiple: false
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('deve renderizar o componente de seleção única', () => {
      render(<InstanceAutocomplete {...defaultProps} />);
      
      expect(screen.getByLabelText('Selecione ou busque uma instância')).toBeInTheDocument();
    });

    test('deve chamar onInstanceChange quando uma instância é selecionada', async () => {
      render(<InstanceAutocomplete {...defaultProps} />);
      
      const autocomplete = screen.getByLabelText('Selecione ou busque uma instância');
      fireEvent.click(autocomplete);
      
      await waitFor(() => {
        expect(screen.getByText('WhatsApp Instance 1 - whatsapp')).toBeInTheDocument();
      });
      
      fireEvent.click(screen.getByText('WhatsApp Instance 1 - whatsapp'));
      
      expect(defaultProps.onInstanceChange).toHaveBeenCalledWith('1');
    });
  });

  describe('Seleção Múltipla', () => {
    const multipleProps = {
      instances: mockInstances,
      selectedInstanceIds: [],
      onInstancesChange: jest.fn(),
      onSelectAll: jest.fn(),
      onClearSelection: jest.fn(),
      disabled: false,
      multiple: true
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    test('deve renderizar o componente de seleção múltipla', () => {
      render(<InstanceAutocomplete {...multipleProps} />);
      
      expect(screen.getByText('Selecionar todos')).toBeInTheDocument();
      expect(screen.getByText('Limpar seleção')).toBeInTheDocument();
    });

    test('deve mostrar todas as instâncias na lista', () => {
      render(<InstanceAutocomplete {...multipleProps} />);
      
      expect(screen.getByTestId('instance-card-1')).toBeInTheDocument();
      expect(screen.getByTestId('instance-card-2')).toBeInTheDocument();
      expect(screen.getByTestId('instance-card-3')).toBeInTheDocument();
    });

    test('deve chamar onInstancesChange quando uma instância é selecionada', () => {
      render(<InstanceAutocomplete {...multipleProps} />);
      
      const checkbox = screen.getAllByRole('checkbox')[0];
      fireEvent.click(checkbox);
      
      expect(multipleProps.onInstancesChange).toHaveBeenCalledWith(['1']);
    });

    test('deve chamar onSelectAll quando o botão "Selecionar todos" é clicado', () => {
      render(<InstanceAutocomplete {...multipleProps} />);
      
      const selectAllButton = screen.getByText('Selecionar todos');
      fireEvent.click(selectAllButton);
      
      expect(multipleProps.onSelectAll).toHaveBeenCalled();
    });

    test('deve chamar onClearSelection quando o botão "Limpar seleção" é clicado', () => {
      const propsWithSelection = {
        ...multipleProps,
        selectedInstanceIds: ['1', '2']
      };
      
      render(<InstanceAutocomplete {...propsWithSelection} />);
      
      const clearButton = screen.getByText('Limpar seleção');
      fireEvent.click(clearButton);
      
      expect(multipleProps.onClearSelection).toHaveBeenCalled();
    });

    test('deve mostrar instâncias selecionadas como chips', () => {
      const propsWithSelection = {
        ...multipleProps,
        selectedInstanceIds: ['1', '2']
      };
      
      render(<InstanceAutocomplete {...propsWithSelection} />);
      
      expect(screen.getByText('Instâncias Selecionadas (2)')).toBeInTheDocument();
      expect(screen.getByText('WhatsApp Instance 1')).toBeInTheDocument();
      expect(screen.getByText('Instagram Instance 1')).toBeInTheDocument();
    });

    test('deve permitir remover instância através do chip', () => {
      const propsWithSelection = {
        ...multipleProps,
        selectedInstanceIds: ['1']
      };
      
      render(<InstanceAutocomplete {...propsWithSelection} />);
      
      const deleteButton = screen.getByTestId('CancelIcon');
      fireEvent.click(deleteButton);
      
      expect(multipleProps.onInstancesChange).toHaveBeenCalledWith([]);
    });

    test('deve desabilitar botões quando disabled=true', () => {
      const disabledProps = {
        ...multipleProps,
        disabled: true
      };
      
      render(<InstanceAutocomplete {...disabledProps} />);
      
      expect(screen.getByText('Selecionar todos')).toBeDisabled();
      expect(screen.getByText('Limpar seleção')).toBeDisabled();
    });
  });
});
