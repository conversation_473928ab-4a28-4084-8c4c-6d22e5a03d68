import {
  Box,
  TextField,
  Typography
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { Autocomplete } from '@material-ui/lab';
import React, { useState } from 'react';

const useStyles = makeStyles((theme) => ({
  root: {
    marginBottom: theme.spacing(2),
  },
  messagePreview: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
  },
  option: {
    fontSize: 14,
    '& > span': {
      marginRight: 10,
      fontSize: 18,
    },
  },
}));

const SearchAutocomplete = ({
  label,
  onTextChange,
  options,
  optionKey,
  optionLabel,
}) => {
  const classes = useStyles();
  const [inputValue, setInputValue] = useState('');


  return (
    <Autocomplete
      options={options}
      getOptionLabel={(option) => optionLabel ? option[optionLabel] : option}
      value={options.find((m) => m[optionKey] === inputValue) || null}
      onChange={(event, newValue) => {
        onTextChange(newValue ? newValue[optionKey] || newValue : '');
      }}
      inputValue={inputValue}
      onInputChange={(event, newInputValue) => {
        setInputValue(newInputValue);
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          label={label}
          variant="outlined"
          fullWidth
        />
      )}
      renderOption={(option) => (
        <Box className={classes.option}>
          <Typography variant="body2">{option[optionLabel] || option}</Typography>
        </Box>
      )}
    />
  );
};

export default SearchAutocomplete;
