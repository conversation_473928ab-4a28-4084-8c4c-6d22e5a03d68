import {
  Box,
  Button,
  Checkbox,
  Chip,
  List,
  ListItem,
  ListItemText,
  Paper,
  TextField,
  Typography
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { Autocomplete } from '@material-ui/lab';
import { langMessages } from 'Lang/index';
import React, { useState } from 'react';
import { InstanceSelectCard } from '../../../../components-new/molecules';

const useStyles = makeStyles((theme) => ({
  root: {
    marginBottom: theme.spacing(2),
  },
  instancePreview: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
  },
  option: {
    fontSize: 14,
    '& > span': {
      marginRight: 10,
      fontSize: 18,
    },
  },
  // Novos estilos para seleção múltipla
  actions: {
    display: 'flex',
    gap: theme.spacing(1),
    marginBottom: theme.spacing(2),
  },
  listContainer: {
    maxHeight: 300,
    overflow: 'auto',
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: theme.shape.borderRadius,
  },
  selectedInstancesContainer: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
  },
  selectedInstancesTitle: {
    marginBottom: theme.spacing(1),
    fontWeight: 'bold',
  },
  chipContainer: {
    display: 'flex',
    flexWrap: 'wrap',
    gap: theme.spacing(1),
  },
  instanceListItem: {
    borderBottom: `1px solid ${theme.palette.divider}`,
    '&:last-child': {
      borderBottom: 'none',
    },
  },
}));

// Componente para seleção única (compatibilidade)
const SingleInstanceSelect = ({
  instances,
  selectedInstanceId,
  onInstanceChange,
  disabled = false,
  classes
}) => {
  const [inputValue, setInputValue] = useState('');

  const selectedInstance = instances.find(
    (m) => m.id.toString() === selectedInstanceId
  );
  console.log('AUTO', instances)
  return (
    <>
      <Autocomplete
        options={instances}
        disabled={disabled}
        getOptionLabel={(option) => option.title}
        value={instances.find((m) => m.id.toString() === selectedInstanceId) || null}
        onChange={(_, newValue) => {
          onInstanceChange(newValue ? newValue.id.toString() : '');
        }}
        inputValue={inputValue}
        onInputChange={(_, newInputValue) => {
          setInputValue(newInputValue);
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            disabled={disabled}
            label={langMessages['shotx.broadcast.instanceSelectLabel']}
            variant="outlined"
            fullWidth
          />
        )}
        renderOption={(option) => (
          <Box className={classes.option}>
            <InstanceSelectCard instance={option} />
          </Box>
        )}
      />
      {selectedInstance && (
        <Box className={classes.instancePreview}>
          <InstanceSelectCard instance={selectedInstance} />
        </Box>
      )}
    </>
  );
};

// Componente para seleção múltipla
const MultipleInstanceSelect = ({
  instances,
  selectedInstanceIds,
  onInstancesChange,
  onSelectAll,
  onClearSelection,
  disabled = false,
  classes
}) => {
  const handleInstanceToggle = (instanceId) => {
    const currentIndex = selectedInstanceIds.indexOf(instanceId);
    const newSelected = [...selectedInstanceIds];

    if (currentIndex === -1) {
      newSelected.push(instanceId);
    } else {
      newSelected.splice(currentIndex, 1);
    }

    onInstancesChange(newSelected);
  };

  const selectedInstances = instances.filter(instance =>
    selectedInstanceIds.includes(instance.id.toString())
  );

  return (
    <>
      <Box className={classes.actions}>
        <Button
          variant="outlined"
          color="primary"
          onClick={onSelectAll}
          disabled={disabled || instances.length === 0}
        >
          {langMessages['shotx.broadcast.selectAll']}
        </Button>
        <Button
          variant="outlined"
          onClick={onClearSelection}
          disabled={disabled || selectedInstanceIds.length === 0}
        >
          {langMessages['shotx.broadcast.selectNone']}
        </Button>
      </Box>

      <List className={classes.listContainer}>
        {instances.map((instance) => (
          <ListItem
            key={instance.id}
            disabled={disabled}
            button
            onClick={() => handleInstanceToggle(instance.id.toString())}
            className={classes.instanceListItem}
          >
            <Checkbox
              checked={selectedInstanceIds.includes(instance.id.toString())}
              disabled={disabled}
            />
            <ListItemText
              primary={
                <Box>
                  <InstanceSelectCard instance={instance} />
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>

      {selectedInstances.length > 0 && (
        <Paper className={classes.selectedInstancesContainer}>
          <Typography variant="subtitle2" className={classes.selectedInstancesTitle}>
            {langMessages['shotx.broadcast.selectedInstances']} ({selectedInstances.length})
          </Typography>
          <Box className={classes.chipContainer}>
            {selectedInstances.map((instance) => (
              <Chip
                key={instance.id}
                label={instance.title}
                color="primary"
                variant="outlined"
                size="small"
                onDelete={disabled ? undefined : () => handleInstanceToggle(instance.id.toString())}
              />
            ))}
          </Box>
        </Paper>
      )}
    </>
  );
};

const Instancecomplete = ({
  instances,
  selectedInstanceId,
  selectedInstanceIds,
  onInstanceChange,
  onInstancesChange,
  onSelectAll,
  onClearSelection,
  disabled = false,
  multiple = false
}) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      {multiple ? (
        <MultipleInstanceSelect
          instances={instances}
          selectedInstanceIds={selectedInstanceIds || []}
          onInstancesChange={onInstancesChange}
          onSelectAll={onSelectAll}
          onClearSelection={onClearSelection}
          disabled={disabled}
          classes={classes}
        />
      ) : (
        <SingleInstanceSelect
          instances={instances}
          selectedInstanceId={selectedInstanceId}
          onInstanceChange={onInstanceChange}
          disabled={disabled}
          classes={classes}
        />
      )}
    </div>
  );
};

export default Instancecomplete;
