import {
  Box,
  TextField,
  Typography
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { Autocomplete } from '@material-ui/lab';
import { langMessages } from 'Lang/index';
import React, { useState } from 'react';

const useStyles = makeStyles((theme) => ({
  root: {
    marginBottom: theme.spacing(2),
  },
  messagePreview: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
  },
  option: {
    fontSize: 14,
    '& > span': {
      marginRight: 10,
      fontSize: 18,
    },
  },
}));

const MessageAutocomplete = ({
  messages,
  selectedMessage,
  onMessageChange,
  disabled = false,
}) => {
  const classes = useStyles();
  const [inputValue, setInputValue] = useState('');

  const selectedMessageContent = messages.find(
    (m) => m?.id === selectedMessage
  )?.message;
  return (
    <div className={classes.root}>
      <Autocomplete
        options={messages}
        disabled={disabled}
        getOptionLabel={(option) => option.title || option.message}
        value={messages.find((m) => m?.id === selectedMessage) || null}
        onChange={(event, newValue) => {
          onMessageChange(newValue ? newValue?.id : '');
        }}
        inputValue={inputValue}
        onInputChange={(event, newInputValue) => {
          setInputValue(newInputValue);
        }}
        renderInput={(params) => (
          < TextField
            {...params}
            disabled={disabled}
            label={langMessages['shotx.broadcast.messageSelectLabel']}
            variant="outlined"
            fullWidth
          />
        )}
        renderOption={(option) => (
          <Box className={classes.option}>
            <Typography variant="body2">{option.title}</Typography>
          </Box>
        )}
      />
      {selectedMessageContent && (
        <Box className={classes.messagePreview}>
          <Typography variant="body2" color="textSecondary">
            {langMessages['shotx.broadcast.messageContent']}:
          </Typography>
          <Typography>{selectedMessageContent}</Typography>
        </Box>
      )}
    </div>
  );
};

export default MessageAutocomplete;
