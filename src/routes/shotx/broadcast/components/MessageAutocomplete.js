import {
  Box,
  Button,
  Checkbox,
  Chip,
  Paper,
  TextField,
  Typography
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { Autocomplete } from '@material-ui/lab';
import { langMessages } from 'Lang/index';
import React, { useState } from 'react';

const useStyles = makeStyles((theme) => ({
  root: {
    marginBottom: theme.spacing(2),
  },
  messagePreview: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
  },
  option: {
    fontSize: 14,
    '& > span': {
      marginRight: 10,
      fontSize: 18,
    },
  },
  // Novos estilos para seleção múltipla
  actions: {
    display: 'flex',
    gap: theme.spacing(1),
    marginBottom: theme.spacing(2),
  },
  listContainer: {
    maxHeight: 300,
    overflow: 'auto',
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: theme.shape.borderRadius,
  },
  selectedMessagesContainer: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
    backgroundColor: theme.palette.grey[50],
    borderRadius: theme.shape.borderRadius,
  },
  selectedMessagesTitle: {
    marginBottom: theme.spacing(1),
    fontWeight: 'bold',
  },
  chipContainer: {
    display: 'flex',
    flexWrap: 'wrap',
    gap: theme.spacing(1),
  },
  messageListItem: {
    borderBottom: `1px solid ${theme.palette.divider}`,
    '&:last-child': {
      borderBottom: 'none',
    },
  },
}));

// Componente para seleção única (compatibilidade)
const SingleMessageSelect = ({
  messages,
  selectedMessage,
  onMessageChange,
  disabled = false,
  classes
}) => {
  const [inputValue, setInputValue] = useState('');

  return (
    <>
      <Autocomplete
        options={messages}
        disabled={disabled}
        getOptionLabel={(option) => option.title || option.message}
        value={messages.find((m) => (m?.id === selectedMessage || m?.ID === selectedMessage)) || null}
        onChange={(_, newValue) => {
          onMessageChange(newValue ? (newValue?.id || newValue?.ID) : '');
        }}
        inputValue={inputValue}
        onInputChange={(_, newInputValue) => {
          setInputValue(newInputValue);
        }}
        renderInput={(params) => (
          <TextField
            {...params}
            disabled={disabled}
            label={langMessages['shotx.broadcast.messageSelectLabel']}
            variant="outlined"
            fullWidth
          />
        )}
        renderOption={(option) => (
          <Box className={classes.option}>
            <Typography variant="body2">{option.title}</Typography>
          </Box>
        )}
      />
      {selectedMessage?.message && (
        <Box className={classes.messagePreview}>
          <Typography variant="body2" color="textSecondary">
            {langMessages['shotx.broadcast.messageContent']}:
          </Typography>
          <Typography>{selectedMessage?.message}</Typography>
        </Box>
      )}
    </>
  );
};

// Componente para seleção múltipla
const MultipleMessageSelect = ({
  messages,
  selectedMessageIds,
  onMessagesChange,
  onSelectAll,
  onClearSelection,
  disabled = false,
  classes
}) => {
  const handleMessageToggle = (messageId) => {
    const currentIndex = selectedMessageIds.indexOf(messageId);
    const newSelected = [...selectedMessageIds];

    if (currentIndex === -1) {
      newSelected.push(messageId);
    } else {
      newSelected.splice(currentIndex, 1);
    }

    onMessagesChange(newSelected);
  };

  const selectedMessages = messages.filter(message =>
    selectedMessageIds.includes((message.id || message.ID).toString())
  );

  return (
    <>
      <Box className={classes.actions}>
        <Button
          variant="outlined"
          color="primary"
          onClick={onSelectAll}
          disabled={disabled || messages.length === 0}
        >
          {langMessages['shotx.broadcast.selectAll']}
        </Button>
        <Button
          variant="outlined"
          onClick={onClearSelection}
          disabled={disabled || selectedMessageIds.length === 0}
        >
          {langMessages['shotx.broadcast.selectNone']}
        </Button>
      </Box>

      <div className={classes.listContainer}>
        {messages.map((message) => (
          <div
            key={message.id || message.ID}
            className={classes.messageListItem}
          >
            <Box
              display="flex"
              alignItems="flex-start"
              p={1}
              style={{ cursor: disabled ? 'default' : 'pointer' }}
              onClick={() => !disabled && handleMessageToggle((message.id || message.ID).toString())}
            >
              <Checkbox
                checked={selectedMessageIds.includes((message.id || message.ID).toString())}
                disabled={disabled}
                color="primary"
              />
              <Box ml={1} flex={1}>
                <Typography variant="subtitle2" gutterBottom>
                  {message.title}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  {message.message && message.message.length > 100
                    ? `${message.message.substring(0, 100)}...`
                    : message.message
                  }
                </Typography>
              </Box>
            </Box>
          </div>
        ))}
      </div>

      {selectedMessages.length > 0 && (
        <Paper className={classes.selectedMessagesContainer}>
          <Typography variant="subtitle2" className={classes.selectedMessagesTitle}>
            {langMessages['shotx.broadcast.selectedMessages']} ({selectedMessages.length})
          </Typography>
          <Box className={classes.chipContainer}>
            {selectedMessages.map((message) => (
              <Chip
                key={message.id || message.ID}
                label={message.title}
                color="primary"
                variant="outlined"
                size="small"
                onDelete={disabled ? undefined : () => handleMessageToggle((message.id || message.ID).toString())}
              />
            ))}
          </Box>
        </Paper>
      )}
    </>
  );
};

const MessageAutocomplete = ({
  messages,
  selectedMessage,
  selectedMessageIds,
  onMessageChange,
  onMessagesChange,
  onSelectAll,
  onClearSelection,
  disabled = false,
  multiple = false
}) => {
  const classes = useStyles();

  return (
    <div className={classes.root}>
      {multiple ? (
        <MultipleMessageSelect
          messages={messages}
          selectedMessageIds={selectedMessageIds || []}
          onMessagesChange={onMessagesChange}
          onSelectAll={onSelectAll}
          onClearSelection={onClearSelection}
          disabled={disabled}
          classes={classes}
        />
      ) : (
        <SingleMessageSelect
          messages={messages}
          selectedMessage={selectedMessage}
          onMessageChange={onMessageChange}
          disabled={disabled}
          classes={classes}
        />
      )}
    </div>
  );
};

export default MessageAutocomplete;
