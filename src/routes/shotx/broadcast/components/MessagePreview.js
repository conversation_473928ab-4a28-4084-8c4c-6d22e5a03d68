import { Box, Paper, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { langMessages } from 'Lang/index';
import React from 'react';

const useStyles = makeStyles((theme) => ({
  previewContainer: {
    padding: theme.spacing(2),
    backgroundColor: theme.palette.grey[50],
  },
  messagePreview: {
    padding: theme.spacing(2),
    marginBottom: theme.spacing(2),
    backgroundColor: theme.palette.background.paper,
    borderRadius: theme.shape.borderRadius,
    border: `1px solid ${theme.palette.divider}`,
  },
}));

const MessagePreview = ({ message, lead }) => {
  const classes = useStyles();
  return (
    <Box className={classes.previewContainer}>
      <Paper className={classes.messagePreview}>
        <Typography variant="subtitle2">
          {langMessages['shotx.broadcast.tagsFilter']}: {lead.displayName} ({lead.mobileCC}{lead.mobile})
        </Typography>
        <Typography variant="body1">
          {message}
        </Typography>
      </Paper>
    </Box>
  );
};

export default MessagePreview;
