import {
  Box,
  Button,
  Checkbox,
  List,
  ListItem,
  ListItemText
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { langMessages } from 'Lang/index';
import React from 'react';
import { Badge } from 'reactstrap';

const useStyles = makeStyles((theme) => ({
  listContainer: {
    maxHeight: 300,
    overflow: 'auto',
  },
  chip: {
    margin: theme.spacing(0.5),
  },
  actions: {
    marginBottom: theme.spacing(2),
    '& > button': {
      marginRight: theme.spacing(2),
    },
  },
}));

const LeadList = ({
  leads,
  selectedLeads,
  onSelectLead,
  onSelectAll,
  onClearSelection,
  selectedTags,
  tags,
  disabled,
}) => {
  const classes = useStyles();

  const filteredLeads = leads.filter(
    (lead) =>
      selectedTags.length === 0 ||
      lead.tags.some((tag) => selectedTags.includes(tag))
  );

  const getTag = (tagid) => {
    return tags.find((tag) => tag.ID === tagid);
  };

  return (
    <>
      <Box className={classes.actions}>
        <Button variant="outlined" color="primary" onClick={onSelectAll} disabled={disabled}>
          {langMessages['shotx.broadcast.selectAll']}
        </Button>
        <Button variant="outlined" onClick={onClearSelection} disabled={disabled}>
          {langMessages['shotx.broadcast.selectNone']}
        </Button>
      </Box>
      <List className={classes.listContainer}>
        {filteredLeads.map((lead) => (
          <ListItem key={lead.id} disabled={disabled} button onClick={() => onSelectLead(lead.id)}>
            <Checkbox checked={selectedLeads.includes(lead.id)} />
            <ListItemText
              primary={lead.displayName}
              secondary={
                <Box>
                  {lead.mobileCC}{lead.mobile}
                  <Box mt={1}>
                    {lead.tags.map((tagid) => {
                      const tag = getTag(tagid);
                      if (!tag) {
                        return null;
                      }
                      return (
                        <Badge
                          key={tagid}
                          size="small"
                          className={`bg-${tag.color} mr-5`}
                          color="primary"
                        >
                          {tag.title}
                        </Badge>
                      );
                    })}
                  </Box>
                </Box>
              }
            />
          </ListItem>
        ))}
      </List>
    </>
  );
};

export default LeadList;
