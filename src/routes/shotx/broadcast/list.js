import { <PERSON>ge, Button, Snackbar, Tooltip } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import AddIcon from '@material-ui/icons/Add';
import { Alert } from '@material-ui/lab';
import { PageTitleBar } from 'Components/index';
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard';
import { Loading } from 'Components/Widgets/components/Loading';
import COLLECTIONS, { SHOTX_CRON_COLLECTION_NAME } from 'Constants/AppCollections';
import { FirebaseRepository } from 'FirebaseRef/repository';
import { langMessages } from 'Lang/index';
import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet';
import { useSelector } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import NoPostsFound from 'Routes/components/NoPostsFound';
import IntlMessages from 'Util/IntlMessages';
import { MessageCard } from '../components/MessageCard';
const useStyles = makeStyles((theme) => ({
    root: {
        minHeight: '100vh',
        minWidth: '100vh',
        padding: theme.spacing(4),
    },
    container: {
        maxWidth: 1280,
        margin: '0 auto',
    },
    header: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: theme.spacing(4),
        backgroundColor: '#2C3644',
        width: '100%',
        padding: theme.spacing(2),
    },
    title: {
        display: 'flex',
        alignItems: 'center',
        gap: theme.spacing(1),

        '& h1': {
            fontSize: '1.5rem',
            fontWeight: 700,
        },
        '& span': {
            backgroundColor: '#ef4444',
            color: '#fff',
            padding: '2px 8px',
            borderRadius: '9999px',
            fontSize: '0.75rem',
        },
    },
    select: {
        width: 180,
        color: '#fff',
        padding: 0,
        '& .MuiSelect-select': {
            padding: theme.spacing(1, 2),
        },
    },
    grid: {
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: theme.spacing(3),
    },
}));



const ListBroadCastPage = ({ match, location }) => {
    const classes = useStyles();
    const [selectedFilter, setSelectedFilter] = useState('todos');
    const isDarkMode = Boolean(localStorage.getItem('config.darkMode'));
    const collections = {
        shotxCron: SHOTX_CRON_COLLECTION_NAME,
    }
    const { account, user } = useSelector(state => state.authReducer);

    const repository = new FirebaseRepository();

    const whereAccountId = [['accountId', '==', account.id]];

    const [shotxCrons, setShotxCrons] = useState([])
    const [loading, setLoading] = useState(true)

    const [snackBarMessage, setSnackBarMessage] = useState(null)

    useEffect(() => {
        if (location.state?.toast) {
            setSnackBarMessage(location.state.toast)
        }
        const fetchData = async () => {
            const listeners = {};
            listeners[collections.shotxCron] = await repository.listenCollection(
                collections.shotxCron,
                (shotxCron) => {
                    setShotxCrons(shotxCron);
                    setLoading(false);
                },
                console.log,
                whereAccountId,
                'createdAt',
                'desc'
            );

            return () => {
                for (const [key, listener] of Object.entries(listeners)) {
                    listener();
                }
            };
        };

        fetchData();
    }, [])

    const filteredMessages = shotxCrons.filter((message) => {
        if (selectedFilter === 'todos') return true;
        if (selectedFilter === 'sniper') return message.type === 'sniper';
        if (selectedFilter === 'texto') return message.type === 'text';
        return true;
    });

    const history = useHistory();

    const handleClick = () => {
        history.push('/shotx/broadcast/add');
    };
    return (
        <>
            <Helmet>
                <title>{langMessages['app.appName'] + ' | ' + langMessages['messagesBroadcast.module.title']}</title>
                <meta name="description" content="QIPlus" />
            </Helmet>
            <PageTitleBar
                title={
                    <div>
                        <Badge badgeContent={filteredMessages.length} color="error">
                            <IntlMessages id={"messagesBroadcast.module.title"} />{' '}
                        </Badge>
                    </div>
                }
                match={match}
                history={history}
                rightComponents={
                    <Button
                        onClick={e => handleClick()}

                    >
                        <Tooltip title={langMessages['actions.add.brodcast']}>
                            <AddIcon />
                        </Tooltip>
                    </Button>
                }
            />
            <div className={classes.root}>
                <div className={classes.container}>
                    <Loading loading={loading} label={langMessages['texts.loading']}>

                        {filteredMessages && filteredMessages.length === 0 ? (
                            <RctCollapsibleCard>
                                <NoPostsFound refresh={() => { }} addPostAction={() => { history.push('/shotx/broadcast/add') }} collection={`${COLLECTIONS.BROADCASTS_COLLECTION_NAME}`} showAdd={false} />
                            </RctCollapsibleCard>) : (
                            <div className={classes.grid}>
                                {filteredMessages.map((message) => (
                                    <MessageCard key={message.ID} message={message} />
                                ))}
                            </div>
                        )
                        }
                    </Loading>
                </div>
            </div>

            <Snackbar
                anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                open={!!snackBarMessage}
                onClose={() => setSnackBarMessage(null)}
                autoHideDuration={5000}
            >
                <Alert onClose={() => setSnackBarMessage(null)} severity={snackBarMessage?.severity}>
                    {snackBarMessage?.message}
                </Alert>
            </Snackbar>

        </>
    );
};

export const ListShotxBroadcast = withRouter(ListBroadCastPage);
