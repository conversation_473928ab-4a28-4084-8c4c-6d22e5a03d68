import { Container, Grid, makeStyles } from '@material-ui/core';
import {
    CheckCircle,
    Error,
    Message,
    People,
    RemoveRedEye,
    Send
} from '@material-ui/icons';
import { ShotxCronActions } from 'Actions/ShotxCronActions';
import { PageTitleBar } from 'Components/index';

import { langMessages } from 'Lang/index';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet';
import { withRouter } from 'react-router-dom';
import { useHistory, useLocation } from 'react-router-dom/cjs/react-router-dom.min';
import { TimezoneUtil } from 'Util/country/timezone';
import { AnalyticsCard } from '../../../components-new/AnalyticsCard';
import DateFiltersForm from '../../../components/DateFilters/DateFiltersForm';
import { Loading } from "../../../components/Widgets/components/Loading";
import Column<PERSON>hart from './components/ColumnChart';
import ReportsContent from './components/ReportsContent';

const AnalyticsBroadcastDetails = ({ match }) => {
    const location = useLocation();
    const history = useHistory();
    const { message } = location.state || {};

    const [startFilterDate, setStartFilterDate] = useState(moment().subtract(1, 'month').valueOf());
    const [endFilterDate, setEndFilterDate] = useState(moment().valueOf());
    const [rangeFilter, setRangeFilter] = useState('last30Days');
    const [logs, setLogs] = useState([]);
    const [viewedMessages, setViewedMessages] = useState(0);
    const [sentMessages, setSentMessages] = useState(0);
    const [deliveredMessages, setDeliveredMessages] = useState(0);
    const [repliedMessages, setRepliedMessages] = useState(0);
    const [notDeliveredMessages, setNotDeliveredMessages] = useState(0);
    const [chartData, setChartData] = useState([]);
    const [chartDataLimitY, setChartDataLimitY] = useState([]);
    const [recipients, setRecipients] = useState((message?.contacts?.length || 0) + (message?.segmentationsIds?.length || 0));
    const [loading, setLoading] = useState(true);
    const theme = localStorage.getItem('config.darkMode');

    const handleUpdateFilters = (filters) => {
        setStartFilterDate(filters.filters.start);
        setEndFilterDate(filters.filters.end);
        setRangeFilter(filters.filters.range);
    }

    const getData = async () => {
        const shotxCronActions = new ShotxCronActions();
        const allLogs = await shotxCronActions.getLogsForBroadcast(message.ID, startFilterDate, endFilterDate)
        if (message.type === 'sniper') {
            filterByContactIdEarliestOnly(allLogs[0])
        } else {
            setLogs(allLogs[0])
            setChartData(organizeLogsForLineChart(allLogs[0]))
        }
    }


    function filterByContactIdEarliestOnly(data) {
        const filteredData = {};

        for (const key in data) {
            const seen = new Map();

            for (const item of data[key]) {
                const existing = seen.get(item.contactId);
                if (!existing || item.createdAt < existing.createdAt) {
                    seen.set(item.contactId, item);
                }
            }

            filteredData[key] = Array.from(seen.values());
        }
        setLogs(filteredData);
        setChartData(organizeLogsForLineChart(filteredData))
    }

    useEffect(() => {
        getData()
    }, [startFilterDate, endFilterDate])

    useEffect(() => {
        setChartDataLimitY(adjustYAxis(chartData))
        setLoading(false)
    }, [chartData])

    useEffect(() => {
        setLoading(true)
        setViewedMessages(logs?.viewed?.length || 0)
        setSentMessages(logs?.sent?.length || 0)
        setDeliveredMessages(logs?.delivered?.length || 0)
        setRepliedMessages(logs?.replied?.length || 0)
        setNotDeliveredMessages(logs?.error?.length || 0)
        console.log('LOGS', logs)
    }, [logs])

    const filters = {
        start: startFilterDate,
        end: endFilterDate,
        range: rangeFilter,
    }

    const useStyles = makeStyles((theme) => ({
        root: {
            paddingTop: theme.spacing(3),
            paddingBottom: theme.spacing(3),
            minHeight: '100vh',
            display: 'flex',
            alignItems: 'flex-start',
        },
        container: {
            maxWidth: '1600px',
        },
        gridItem: {
            display: 'flex',
            width: '100%',
        },
    }));

    const classes = useStyles();

    const analyticsData = [
        {
            title: langMessages['reports.recipients'],
            value: recipients,
            icon: <People fontSize="large" />,
            iconColor: 'rgb(25, 118, 210)',
        },
        {
            title: langMessages['shotx.broadcast.sent'],
            value: sentMessages,
            icon: <Send fontSize="large" />,
            iconColor: 'rgb(76, 175, 80)',
        },
        {
            title: langMessages['reports.delivered'],
            value: deliveredMessages,
            icon: <CheckCircle fontSize="large" />,
            iconColor: 'rgb(0, 200, 83)',
        },
        {
            title: langMessages['reports.viewed'],
            value: viewedMessages,
            icon: <RemoveRedEye fontSize="large" />,
            iconColor: 'rgb(156, 39, 176)',
        },
        {
            title: langMessages['shotx.broadcast.replieds'],
            value: repliedMessages,
            icon: <Message fontSize="large" />,
            iconColor: 'rgb(255, 152, 0)',
        },
        {
            title: langMessages['shotx.broadcast.failed'],
            value: notDeliveredMessages,
            icon: <Error fontSize="large" />,
            iconColor: 'rgb(244, 67, 54)',
        },
    ];


    const organizeLogsForLineChart = (data) => {
        const groupedData = {};

        Object.keys(data).forEach((category) => {
            data[category].forEach((item) => {
                const date = TimezoneUtil.formatDate(TimezoneUtil.convertToUserTimezone(item.createdAt * 1000), langMessages['format.date'])

                if (!groupedData[date]) groupedData[date] = { viewed: 0, sent: 0, delivered: 0, replied: 0, error: 0 };
                groupedData[date][category]++;
            });
        });

        const chartData = Object.keys(groupedData).map((date) => ({
            date,
            ...groupedData[date],
        }));


        const rawData = [chartData]
        const type = message.instance.platform.toLowerCase()
        const formattedData = [
            [
                langMessages['widgets.date'],
                langMessages['shotx.broadcast.sent'],
                ...(type !== 'instagram' ? [langMessages['reports.delivered']] : []),
                // langMessages['reports.delivered'],
                langMessages['reports.viewed'],
                langMessages['shotx.broadcast.replieds'],
                langMessages['shotx.broadcast.failed']
            ],
        ];

        rawData.map(item => [
            item.map(value => {
                if (type !== 'instagram') {
                    formattedData.push(
                        [
                            value?.date || 0,        // Data
                            value?.sent || 0,        // Enviado
                            value?.delivered || 0,   // Entregue
                            value?.viewed || 0,      // Visualizado
                            value?.replied || 0,      // Respondido
                            value?.error || 0      // Error
                        ]
                    )
                } else {
                    formattedData.push(
                        [
                            value?.date || 0,        // Data
                            value?.sent || 0,        // Enviado
                            value?.viewed || 0,      // Visualizado
                            value?.replied || 0,      // Respondido
                            value?.error || 0      // Error
                        ]
                    )
                }
            }
            ),
        ])
        return formattedData
    }

    const optionsChart = {
        title: langMessages['shotx.broadcast.indicators'],
        titleTextStyle: {
            color: theme ? 'white' : 'black',
        },
        chartArea: { width: '85%', height: '70%' },
        colors: message?.instance?.platform.toLowerCase() === 'instagram' ? ['#ff9800', '#3f51b5', '#00D0BD', '#f44336'] : ['#ff9800', '#00D0BD', '#3f51b5', '#25D366', '#f44336'],
        backgroundColor: 'transparent',
        responsive: true,
        vAxis: {
            viewWindow: {
                min: chartDataLimitY.yMin,
                max: chartDataLimitY.yMax
            },
            textStyle: {
                color: theme ? 'white' : 'black',
            },
        },
        hAxis: {
            textStyle: {
                color: theme ? 'white' : 'black',
            },
        },
        legend: {
            position: 'bottom',
            textStyle: {
                color: theme ? 'white' : 'black',
            },
        },
    }

    function adjustYAxis(data) {

        if (!data || data.length === 0) {
            return { yMin: 0, yMax: 10 };
        }

        const allValues = data.flatMap(item => {
            if (Array.isArray(item) && item.length > 1) {
                return item.slice(1).map(value => (typeof value === 'number' ? value : 0));
            }
            return [];
        });

        if (allValues.length === 0) {
            return { yMin: 0, yMax: 10 };
        }

        const minValue = Math.min(...allValues);
        const maxValue = Math.max(...allValues);

        const range = maxValue - minValue;
        const margin = range * 0.1;

        const yMin = Math.max(0, Math.floor(minValue - margin));
        const yMax = Math.ceil(maxValue + margin);
        if (yMax < 10) {
            return { yMin, yMax: 10 };
        }
        return { yMin, yMax };
    }

    return (
        <>
            <Helmet>
                <title>{langMessages['app.appName'] + ' | ' + langMessages['messagesBroadcast.module.title']}</title>
                <meta name="description" content="QIPlus" />
            </Helmet>
            <PageTitleBar
                title={
                    <div>
                        {langMessages['shotx.broadcast.title.analytcs'].replace('[%type]', message.type.toUpperCase()).replace('[%platform]', message.instance.platform).replace('[%date_schedule]', TimezoneUtil.formatDate(TimezoneUtil.convertToUserTimezone(message.scheduled_date), langMessages['format.date.full.short.year']))}
                    </div>
                }
                match={match}
                history={history}
                rightComponents={
                    <DateFiltersForm filters={filters} onUpdate={filters => handleUpdateFilters({ filters })} />
                }
            />

            <Container className={classes.root} classes={{ maxWidthLg: classes.container }}>
                <Grid container spacing={4}>
                    {analyticsData.map((item, index) => (
                        <Grid item xs={12} sm={6} md={4} key={index} className={classes.gridItem}>
                            <AnalyticsCard
                                title={item.title}
                                value={item.value}
                                icon={item.icon}
                            // iconColor={item.iconColor}
                            />
                        </Grid>
                    ))}
                    <Grid item xs={12}>
                        <Loading loading={loading} label={langMessages["texts.loading"]}>
                            <ColumnChart xs={12}
                                chartData={chartData}
                                optionsChart={optionsChart}
                            />
                        </Loading>
                    </Grid>

                    <Grid item xs={12}>
                        <Loading loading={loading} label={langMessages["texts.loading"]}>
                            <ReportsContent
                                message={message}
                            />
                        </Loading>
                    </Grid>
                </Grid>
            </Container >
        </>
    )
}

export const AnalyticsBroadcast = withRouter(AnalyticsBroadcastDetails)
