/**
 * RecipientTab Component
 * Componente wrapper para a aba "recipient" que usa o novo componente RecipientTabFirebase
 */
import React from 'react';
import RecipientTabFirebase from './RecipientTabFirebase';

const RecipientTab = (props) => {
  // Função para atualizar as configurações
  const handleUpdateSettings = (newSettings) => {
    if (props.onUpdateField) {
      // Atualiza os contatos
      if (newSettings.contacts !== undefined) {
        // Criar um objeto com a propriedade 'contacts' para o updatePost
        props.onUpdateField({ contacts: newSettings.contacts }, 'settings');
      }

      // Atualiza as segmentações
      if (newSettings.segmentations !== undefined) {
        // Criar um objeto com a propriedade 'segmentations' para o updatePost
        props.onUpdateField({ segmentations: newSettings.segmentations }, 'settings');
      }
    }
  };

  return <RecipientTabFirebase {...props} onUpdateSettings={handleUpdateSettings} />;
};

export default RecipientTab;
