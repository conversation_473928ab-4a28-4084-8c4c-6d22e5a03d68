import {
  Button,
  ButtonGroup,
  makeStyles,
  Tooltip,
  Typography
} from '@material-ui/core';
import {
  Add as AddIcon,
  ViewModule as GridIcon,
  ViewList as ListIcon,
} from '@material-ui/icons';
import { langMessages } from 'Lang/index';
import React from 'react';

const useStyles = makeStyles((theme) => ({
  header: {
    marginBottom: theme.spacing(4),
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    '& h1': {
      fontSize: '2rem',
      fontWeight: 'bold',
    },
    '& p': {
      color: theme.palette.text.secondary,
    },
  },
  viewToggle: {
    marginLeft: theme.spacing(2),
  },
}));

/**
 * Componente que renderiza o titulo da pgina e o boto para adicionar um novo pixel
 * @param {string} title - Titulo da pgina
 * @param {string} subtitle - Subtitulo da pgina
 * @param {function} onAddClick - Fun o que ser  chamada quando o boto for clicado
 * @param {string} viewMode - Modo de visualiza o, pode ser 'grid' ou 'list'
 * @param {function} onChangeView - Fun o que ser  chamada quando o modo de visualiza o for alterado
 * @returns {ReactElement}
 * @example
 * Exemplo de uso:
 * <TitleBar
 * title={langMessages['campaigns.hunter.title']}
 * subtitle={langMessages['campaigns.hunter.description']}
 * onAddClick={() => setIsFormOpen(true)}
 * viewMode={viewMode}
 * onChangeView={setViewMode}
 * />
 */
export const TitleBar = ({ title, subtitle, onAddClick, viewMode = 'grid', onChangeView }) => {
  const classes = useStyles();

  return (
    <div className={classes.header}>
      <div className={classes.title}>
        <Typography variant="h1">{title}</Typography>
        <Typography variant="body1">{subtitle}</Typography>
      </div>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {onAddClick && (
          <Button
            variant="outlined"
            className='bg-qiplus text-white'
            startIcon={<AddIcon />}
            onClick={onAddClick}
          >
            {langMessages['campaigns.hunter.new']}
          </Button>
        )}
        <ButtonGroup className={classes.viewToggle} variant="outlined">
          <Tooltip title={langMessages['campaigns.hunter.listView']}>
            <Button
              className={viewMode === 'list' ? 'bg-qiplus text-white' : ''}
              onClick={() => onChangeView('list')}
            >
              <ListIcon />
            </Button>
          </Tooltip>
          <Tooltip title={langMessages['campaigns.hunter.gridView']}>
            <Button
              className={viewMode === 'grid' ? 'bg-qiplus text-white' : ''}
              onClick={() => onChangeView('grid')}
            >
              <GridIcon />
            </Button>
          </Tooltip>
        </ButtonGroup>
      </div>
    </div>
  );
}
