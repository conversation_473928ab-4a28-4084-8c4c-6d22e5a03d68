import {
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  InputLabel,
  TextField
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { Loading } from 'Components/Widgets/components/Loading';
import { langMessages } from 'Lang/index';
import React, { useState } from 'react';
import { HunterEvents } from '../model/HunterEvents';
import TagInput from './PixelTag';

const DEFAULT_EVENTS = Object.keys(HunterEvents);

const PixelForm = ({ pixel, onSubmit, onCancel, loading = false }) => {
  const events = pixel?.events || DEFAULT_EVENTS;
  const [formData, setFormData] = useState({
    name: pixel?.name || '',
    description: pixel?.description || '',
    domain: pixel?.domain || '',
    events: pixel?.events || DEFAULT_EVENTS,
    customAttributes: pixel?.customAttributes || [],
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const pixelData = {
      ...formData,
      id: pixel?.id || null,
      active: pixel?.active ?? true,
      createdAt: pixel?.createdAt || new Date(),
      updatedAt: new Date(),
    };
    onSubmit(pixelData);
  };

  const handleSelectedEvent = (event) => {
    if (formData.events.includes(event)) {
      setFormData({
        ...formData,
        events: formData.events.filter((e) => e !== event),
      });
    } else {
      setFormData({
        ...formData,
        events: [...formData.events, event],
      });
    }
  };

  const eventChanged = DEFAULT_EVENTS
    .map((event) => formData.events.includes(event) !== events.includes(event))
    .includes(true);

  return (
    <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <TextField
        label={langMessages['campaigns.hunter.name']}
        value={formData.name}
        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
        required
        fullWidth
      />

      <TextField
        label={langMessages['campaigns.hunter.newDescription']}
        value={formData.description}
        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
        multiline
        minRows={3}
        fullWidth
      />

      <TextField
        label={langMessages['campaigns.hunter.domain']}
        type="url"
        value={formData.domain}
        onChange={(e) => setFormData({ ...formData, domain: e.target.value })}
        required
        fullWidth
      />

      {eventChanged && (
        <Alert severity="warning">
          {langMessages['campaigns.hunter.events.warning']}
        </Alert>
      )}

      <div>
        {/* Events */}
        <InputLabel component="legend">{langMessages['campaigns.hunter.events.select']}</InputLabel>
        <FormControl fullWidth>
          <div style={{ display: 'flex', flexDirection: 'row', gap: '0.5rem', marginTop: '0.5rem' }}>
            {Object.entries(HunterEvents).map(([event, { label, key }]) => (
              <FormControlLabel
                key={event}
                control={
                  <Checkbox
                    checked={formData.events.includes(event)}
                    onChange={() => handleSelectedEvent(event)}
                    name={event}
                  />
                }
                label={label}
              />
            ))}
          </div>
        </FormControl>
      </div>

      <div>
        <InputLabel>{langMessages['campaigns.hunter.tags']}</InputLabel>
        <FormControl fullWidth>
          <TagInput
            placeholder={langMessages['campaigns.hunter.tags']}
            value={formData.customAttributes}
            onChange={(tags) => setFormData({ ...formData, customAttributes: tags })}
          />
        </FormControl>
      </div>

      <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '1rem', marginTop: '1rem' }}>
        <Loading loading={loading} title={langMessages['campaigns.hunter.creating']}>
          <Button onClick={onCancel} color="primary">
            {langMessages['campaigns.hunter.cancel']}
          </Button>
          <Button type="submit" variant="contained" color="primary">
            {pixel ? langMessages['campaigns.hunter.update'] : langMessages['campaigns.hunter.create']}
          </Button>
        </Loading>
      </div>
    </form>
  );
};

export default PixelForm;
