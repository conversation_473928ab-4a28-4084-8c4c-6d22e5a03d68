import {
  Chip,
  makeStyles,
  Paper,
  TextField,
} from "@material-ui/core";
import React, { useState } from "react";

const useStyles = makeStyles((theme) => ({
  root: {
    display: "flex",
    flexWrap: "wrap",
    gap: theme.spacing(1),
    padding: theme.spacing(1),
  },
  chipInput: {
    display: "flex",
    alignItems: "center",
    flexWrap: "wrap",
    gap: theme.spacing(1),
  },
}));

const TagInput = ({ placeholder = "Add a tag...", value = [], onChange }) => {
  const classes = useStyles();
  const [tags, setTags] = useState(value);
  const [inputValue, setInputValue] = useState("");

  const handleAddTag = () => {
    if (inputValue.trim() && !tags.includes(inputValue.trim())) {
      const newTags = [...tags, inputValue.trim()];
      setTags(newTags);
      setInputValue("");
      if (onChange) onChange(newTags);
    }
  };

  const handleDeleteTag = (tagToDelete) => {
    const newTags = tags.filter((tag) => tag !== tagToDelete);
    setTags(newTags);
    if (onChange) onChange(newTags);
  };

  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      event.preventDefault();
      handleAddTag();
    }
  };

  return (
    <Paper className={classes.root} elevation={1}>
      <div className={classes.chipInput}>
        {tags.map((tag, index) => (
          <Chip
            key={index}
            label={tag}
            onDelete={() => handleDeleteTag(tag)}
            color="secondary"
            variant="outlined"
          />
        ))}
        <TextField
          placeholder={placeholder}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyPress}
          fullWidth
          InputProps={{ disableUnderline: true }}
        />
      </div>
    </Paper>
  );
};

export default TagInput;
