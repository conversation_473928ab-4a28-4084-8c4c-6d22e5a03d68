import { Button, <PERSON>conButton, makeStyles, Typography, useTheme } from '@material-ui/core';
import { ArrowBackIos } from '@material-ui/icons';
import { langMessages } from 'Lang/index';
import React, { useState } from 'react';
import Chart from 'react-google-charts';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, ModalHeader } from 'reactstrap';

const useStyles = makeStyles((theme) => ({
  root: {
    marginBottom: '40px',
  },
  paper: {
    height: '100%',
    transition: 'transform 0.2s ease-in-out',
    '&:hover': {
      transform: 'translateY(-4px)',
    },
  },
  title: {
    padding: theme.spacing(2),
  },
  gridContainer: {
    padding: theme.spacing(2),
  },
  legends: {
    padding: theme.spacing(2),
  },
  breadcrumb: {
    padding: theme.spacing(1),
    marginBottom: theme.spacing(2),
  },
  breadcrumbLink: {
    cursor: 'pointer',
    '&:hover': {
      textDecoration: 'underline',
    },
    alignItems: 'center',
    gap: theme.spacing(0.5),
  },
  chartContainer: {
    position: 'relative',
    width: '100%',
    height: '400px',
    overflow: 'hidden',
  },
  chart: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    transition: 'transform 0.5s ease-in-out',
  },
  slideOut: {
    transform: 'translateX(-100%)',
  },
  slideIn: {
    transform: 'translateX(0)',
  },
  hidden: {
    transform: 'translateX(100%)',
  },
  noData: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    fontSize: '1.2rem',
  },
}));

const ClickDetails = ({ logs = [] }) => {

  const isDark = useTheme().palette.type === 'dark';
  const classes = useStyles();

  const [isOpen, setIsOpen] = useState(false);
  const [element, setElement] = useState(null);

  const toggle = () => {
    if (isOpen == true) {
      setElement(null)
    }
    setIsOpen(!isOpen)
  };

  const clicksData = logs
    .filter((log) => log.trigger === 'clicked')
    .reduce((acc, log) => {
      const { eltag, elid = 'n/a' } = log.metadata

      if (!eltag) return acc

      if (!acc[eltag]) {
        acc[eltag] = { name: eltag, ids: {}, count: 0 }
      }
      acc[eltag].count += 1

      if (!elid) return acc

      if (!acc[eltag].ids[elid]) {
        acc[eltag].ids[elid] = { name: elid, count: 0 }
      }
      acc[eltag].ids[elid].count += 1

      return acc
    }, {})

  const elementsData = [
    ["Tag", "Cliques"],
    ...Object.values(clicksData).map(({ count, name }) => [name, count])
  ]

  const getElementIdsData = (tag) => {
    if (!clicksData[tag]) return [["Id", "Cliques"]]
    return [
      ["Id", "Cliques"],
      ...Object.values(clicksData[tag].ids).map(({ count, name }) => [name, count])
    ]
  }

  const handleSelect = (chartWrapper) => {
    const chart = chartWrapper.getChart();
    const selection = chart.getSelection();

    if (selection.length > 0) {
      const selectedItem = selection[0]; // Captura o índice da seleção
      const selectedRow = selectedItem.row;

      const tag = elementsData[selectedRow + 1][0];
      // const cliques = elementsData[selectedRow + 1][1];

      setElement(tag);
    }
  };

  return (
    <div>
      <Button onClick={toggle}>{langMessages['button.viewMore']}</Button>
      <Modal isOpen={isOpen} toggle={toggle}>
        <ModalHeader toggle={toggle}>
          <div className={"d-flex flex-row justify-content-start align-items-center"}>
            {element && (
              <IconButton
                color="inherit"
                className={classes.breadcrumbLink}
                disabled={!element}
                onClick={() => element && setElement(null)}
              >
                <ArrowBackIos />
              </IconButton>
            )}
            {langMessages['campaigns.hunter.analytics.clickDetails.title']}
          </div>
        </ModalHeader>
        <ModalBody>
          {!element ? (
            <div className={"d-flex flex-column justify-content-start align-items-center"}>
              <Typography variant="h6" className={classes.title}>{langMessages['campaigns.hunter.analytics.clickDetails.byElement']}</Typography>
              <Typography variant="body2" color="textSecondary" className={classes.description}>{langMessages['campaigns.hunter.analytics.clickDetails.byElementDescription']}</Typography>
            </div>
          ) : (
            <div className={"d-flex flex-row justify-content-center align-items-center"}>
              <Typography color="textPrimary">
                {langMessages['campaigns.hunter.analytics.clickDetails.byElementId'].replace(':element', element)}
              </Typography>
            </div>
          )}
          <div className={classes.chartContainer}>
            <div className={`${classes.chart} ${element ? classes.slideOut : classes.slideIn}`}>
              {elementsData.length > 1 ? (
                <Chart
                  chartType="PieChart"
                  data={elementsData}
                  options={{
                    pieHole: 0.4,
                    backgroundColor: 'transparent',
                    titleTextStyle: { color: isDark ? '#fff' : '#000' },
                    legendTextStyle: { color: isDark ? '#fff' : '#000' },
                  }}
                  chartEvents={[
                    {
                      eventName: "select",
                      callback: ({ chartWrapper }) => handleSelect(chartWrapper),
                    }
                  ]}
                  width={"100%"}
                  height={"100%"}
                />
              ) : (
                <div className={classes.noData}>
                  <Typography
                    variant="body1"
                    color="textSecondary"
                  >
                    {langMessages['campaigns.hunter.analytics.noClicks']}
                  </Typography>
                </div>
              )}
            </div>
            <div className={`${classes.chart} ${element ? classes.slideIn : classes.hidden}`}>
              {getElementIdsData(element).length > 1 ? (
                <Chart
                  chartType="PieChart"
                  data={getElementIdsData(element)}
                  options={{
                    pieHole: 0.4,
                    backgroundColor: 'transparent',
                    titleTextStyle: { color: isDark ? '#fff' : '#000' },
                    legendTextStyle: { color: isDark ? '#fff' : '#000' },
                  }}
                  width={"100%"}
                  height={"100%"}
                />
              ) : (
                <div className={classes.noData}>
                  <Typography
                    variant="body1"
                    color="textSecondary"
                  >
                    {langMessages['campaigns.hunter.analytics.noData']}
                  </Typography>
                </div>
              )}
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="contained"
            color="primary"
            onClick={toggle}
          >
            {langMessages['button.close']}
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default ClickDetails;
