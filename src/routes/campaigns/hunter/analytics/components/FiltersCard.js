import { FormControl, InputLabel, makeStyles, MenuItem, Paper, Select, TextField, Tooltip } from '@material-ui/core';
import { Autocomplete } from '@material-ui/lab';
import { langMessages } from 'Lang/index';
import React from 'react';

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(3),
    height: '100%',
    transition: 'transform 0.2s ease-in-out',
    '&:hover': {
      transform: 'translateY(-4px)',
    },
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 150,
  },
}));

export const FiltersCard = ({ filters, onFilterChange }) => {
  const classes = useStyles();

  const [groupedFilters, setGroupedFilters] = React.useState({});

  const renderSelect = (key, label, filterBy, options, tooltip, placeholder, defaultValue, value, onChange = (e) => onFilterChange(filterBy, e.target.value)) => (
    <div key={`filter-${key}`} className="pr-2">
      <FormControl variant="outlined" key={key} className={classes.formControl}>
        <InputLabel htmlFor="grouped-select">{label}</InputLabel>
        <Tooltip title={tooltip} placement="top">
          <Select
            id="grouped-select"
            size="small"
            key={key}
            value={value}
            defaultValue={defaultValue}
            onChange={(e) => onChange(filterBy, e.target.value)}
          >
            {placeholder && (
              <MenuItem value="">
                <em>{placeholder}</em>
              </MenuItem>
            )}
            {options.map(({ value, label }) => (
              <MenuItem key={value} value={value}>
                {label}
              </MenuItem>
            ))}
          </Select>
        </Tooltip>
      </FormControl>
    </div>
  )

  const renderAutoComplete = (key, label, filterBy, options, disabled = false, onChange = (e, option) => onFilterChange(filterBy, option?.value || '')) => (
    <div key={`filter-${key}`} className="pr-2">
      <Autocomplete
        id={key}
        key={key}
        options={options}
        getOptionLabel={(option) => option.label}
        style={{ width: 300 }}
        renderInput={(params) => <TextField {...params} label={label} variant="outlined" />}
        onChange={onChange}
        getOptionSelected={(option, value) => option.value === value.value}
        disabled={disabled}
        clearText={langMessages['campaigns.hunter.analytics.filters.clear']}
        loadingText={langMessages['campaigns.hunter.analytics.filters.loading']}
        noOptionsText={langMessages['campaigns.hunter.analytics.filters.noOptions']}
        openText={langMessages['campaigns.hunter.analytics.filters.open']}
      />
    </div>
  )

  return (
    <Paper className={classes.root}>
      <div className={'d-flex flex-row align-items-center justify-content-between'}>
        <div className="flex-1 w-xs-half-block">
          <h4>{langMessages['campaigns.hunter.analytics.filters']}</h4>
        </div>

        <div className="flex-1 w-xs-half-block d-flex align-items-center justify-content-end">
          {Object.entries(filters).map(([key, { type, defaultValue, value, options, label, filterBy, placeholder, tooltip, children }]) => {
            switch (type) {
              case 'select':
                return renderSelect(key, label, filterBy, options, tooltip, placeholder, defaultValue, value)
              case 'autocomplete':
                return renderAutoComplete(key, label, filterBy, options)
              case 'grouped-select':
                return (
                  <div key={`filter-${key}`} className="pr-2">
                    {Object.entries(children).map(([key, { options, label }]) => {
                      return renderSelect(key, label, filterBy, options, tooltip, placeholder, defaultValue, value)
                    })}
                  </div>
                )
              case 'grouped-autocomplete':
                return (
                  <div key={`filter-${key}`} className="flex-1 w-xs-half-block d-flex align-items-center justify-content-end">
                    {Object.entries(children).map(([key, { options, label }], id) => {

                      const handleFiltersChange = async (_, option) => {
                        const updatedFilters = { ...groupedFilters }
                        const value = option?.value || ''

                        const applyFilter = (updatedFilters, ifCompleted) => {
                          const values = Object.values(updatedFilters)
                          const isCompleted = values.length === Object.keys(children).length
                          if (ifCompleted && !isCompleted) return
                          const newFilterBy = [filterBy, ...values.slice(0, values.length - 1)].join('.')
                          onFilterChange(newFilterBy, value);
                        }

                        // Se já tem filtro
                        const isDeletion = updatedFilters[id] && !value
                        if (updatedFilters[id]) {
                          if (!value) { // E o valor é nulo, então deve ser deletado
                            applyFilter(updatedFilters, true)
                            delete updatedFilters[id]
                          } else {
                            updatedFilters[id] = value // Atualizar filtro
                          }
                        } else { // Se nao tem filtro
                          if (value) { // Se tem valor
                            updatedFilters[id] = value // Adicionar filtro
                          }
                        }

                        if (!isDeletion) applyFilter(updatedFilters, true)

                        setGroupedFilters(updatedFilters)
                      }

                      const lastValue = groupedFilters[id - 1]
                      const nextedOptions = lastValue ? options.filter(option => option.params === lastValue) : options
                      const disabled = !!groupedFilters[id + 1] || (id > 0 && !lastValue)

                      return renderAutoComplete(key, label, filterBy, nextedOptions, disabled, handleFiltersChange)
                    })}
                  </div>
                )
              default:
                return null;
            }
          })}
        </div>
      </div>
    </Paper>
  );
};
