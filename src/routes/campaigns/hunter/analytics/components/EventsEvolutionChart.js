import { makeStyles, Paper, Typography } from '@material-ui/core';
import { getTheDate } from 'Helpers';
import { langMessages } from 'Lang/index';
import React from 'react';
import { CartesianGrid, Legend, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { HunterEvents } from '../../model/HunterEvents';

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(3),
    height: '100%',
    transition: 'transform 0.2s ease-in-out',
    '&:hover': {
      transform: 'translateY(-4px)',
    },
  },
}));

const EventsEvolutionChart = ({ logs = [] }) => {

  const events = Object.values(HunterEvents).reduce((acc, ev) => {
    if (!acc[ev.key]) {
      acc[ev.key] = 0;
    }
    return acc
  }, {})

  const data = logs.reduce((acc, log) => {
    const monthYear = getTheDate(log.createdAt / 1000, langMessages['format.date']);
    if (!acc[monthYear]) {
      acc[monthYear] = { date: monthYear, createdAt: log.createdAt, ...events };
    }

    acc[monthYear][log.trigger] += 1

    return acc;
  }, {})

  const chartData = Object.values(data).sort((a, b) => a.createdAt - b.createdAt);
  const classes = useStyles();

  return (
    <Paper className={classes.root}>
      <Typography variant="h6" gutterBottom>
        {langMessages['campaigns.hunter.analytics.eventsEvolution.title']}
      </Typography>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart
          width={500}
          height={300}
          data={chartData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis yAxisId="left" />
          <YAxis yAxisId="right" orientation="right" />
          <Tooltip
            contentStyle={{ backgroundColor: 'transparent', border: 'none' }}
            itemStyle={{ backgroundColor: 'transparent', border: 'none' }}
            cursor={{ fill: 'transparent' }}
          />
          <Legend />
          {Object.entries(HunterEvents).map(([_, { label, key, color }]) =>
            <Line
              key={key}
              yAxisId="left"
              type="monotone"
              dataKey={key}
              stroke={color}
              activeDot={{ r: 8 }}
              name={label}
            />
          )}
        </LineChart>
      </ResponsiveContainer>
    </Paper>
  );
};

export default EventsEvolutionChart;
