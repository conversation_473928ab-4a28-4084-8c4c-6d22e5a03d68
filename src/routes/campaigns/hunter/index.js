import {
  Box,
  Button,
  ButtonGroup,
  CircularProgress,
  Container,
  Dialog,
  DialogContent,
  DialogTitle,
  makeStyles,
  Tooltip
} from '@material-ui/core';
import {
  Add as AddIcon,
  ViewModule as GridIcon,
  ViewList as ListIcon,
} from '@material-ui/icons';
import { PageTitleBar } from 'Components/index';
import COLLECTIONS from 'Constants/AppCollections';
import { FirebaseRepository } from 'FirebaseRef/repository';
import { useSnackbar } from 'Hooks/useSnackbar';
import { langMessages } from 'Lang/index';
import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet';
import { withRouter } from 'react-router-dom/cjs/react-router-dom.min';
import PixelCode from './components/PixelCode';
import PixelForm from './components/PixelForm';
import PixelGrid from './components/PixelGrid';
import PixelTable from './components/PixelTable';

const useStyles = makeStyles((theme) => ({
  header: {
    marginBottom: theme.spacing(4),
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    '& h1': {
      fontSize: '2rem',
      fontWeight: 'bold',
    },
    '& p': {
      color: theme.palette.text.secondary,
    },
  },
  viewToggle: {
    marginLeft: theme.spacing(2),
  },
}));

const PixelHunterPage = ({ history, match, location }) => {
  const classes = useStyles();
  const { showSnackbar } = useSnackbar();

  const collectionName = COLLECTIONS.HUNTERS_COLLECTION_NAME;
  const collectionPath = `${collectionName}/`

  const repository = new FirebaseRepository();
  const account = JSON.parse(localStorage.getItem('account', false))

  const [loadingPixels, setLoadingPixels] = useState(true);
  const [pixels, setPixels] = useState([]);
  const [creatingPixel, setCreatingPixel] = useState(false);

  const [selectedPixel, setSelectedPixel] = useState(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isCodeOpen, setIsCodeOpen] = useState(false);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' ou 'list'

  useEffect(() => {
    location.state?.toast && showSnackbar(location.state.toast.message, location.state.toast.severity);
    const onChange = (pixels) => {

      pixels = pixels
        .filter(({ id }) => id)
        .map((pixel) => {
          return {
            ...pixel,
            counts: pixel.events.reduce((acc, event) => {
              acc[event] = pixel?.counts?.[event] || 0;
              return acc;
            }, {})
          };
        });

      pixels.sort((a, b) => {
        return b.createdAt - a.createdAt;
      });

      setPixels(pixels);
      setLoadingPixels(false);
    }

    const onError = () => setLoadingPixels(false);

    const where = [
      ['accountId', '==', account.ID]
    ]

    const unsubscribe = repository.listenCollection(collectionPath, onChange, onError, where);

    return () => {
      unsubscribe();
    };

  }, []);

  const handleCreatePixel = (pixelData) => {
    pixelData.accountId = account.ID
    setCreatingPixel(true);

    repository.addDoc(collectionPath, pixelData)
      .then(result => {
        const { id } = result;
        setSelectedPixel({ ...pixelData, id });
        setIsFormOpen(false);
        showSnackbar(langMessages['campaigns.hunter.created'], 'success');
      })
      .catch((error) => {
        showSnackbar(langMessages['campaigns.hunter.createFailed'], 'error');
      })
      .finally(() => {
        setCreatingPixel(false);
      })
  };

  const handleUpdatePixel = (pixelData) => {
    pixelData.updatedAt = new Date().getTime();
    repository.setDoc(`${collectionName}/${pixelData.id}`, pixelData)
      .then(() => {
        setSelectedPixel(null);
        setIsFormOpen(false);
        showSnackbar(langMessages['campaigns.hunter.updated'], 'success');
      })
      .catch(() => {
        showSnackbar(langMessages['campaigns.hunter.updateFailed'], 'error');
      })
      .finally(() => {
        setCreatingPixel(false);
      });
  };

  const handleDeletePixel = (id) => {
    repository.deleteDoc(`${collectionName}/${id}`);
  };

  const handleTogglePixel = async (id, active) => {
    repository.setDoc(`${collectionName}/${id}`, { active });
  };

  const handleEditPixel = (pixel) => {
    setSelectedPixel(pixel);
    setIsFormOpen(true);
  };

  const handleViewCode = (pixel) => {
    setSelectedPixel(pixel);
    setIsCodeOpen(true);
  };

  const handleClonePixel = (pixel) => {
    const cloneTime = new Date().getTime();
    const clonedPixel = {
      ...pixel,
      name: `${pixel.name} (${langMessages['campaigns.hunter.clone']})`,
      createdAt: cloneTime,
      updatedAt: cloneTime,
      active: false
    };

    delete clonedPixel.id;

    repository.addDoc(collectionPath, clonedPixel);
  };

  const handleStats = (pixel) => {
    history.push(`${match.url}/analytics/${pixel.id}`.replace('//', '/'));
  }

  return (
    <div className="ecom-dashboard-wrapper">
      <Helmet>
        <title>{langMessages['campaigns.hunter.title']}</title>
        <meta name="description" content="QIPlus || Hunter" />
      </Helmet>
      <PageTitleBar
        title={langMessages['campaigns.hunter.title']}
        subtitle={langMessages['campaigns.hunter.description']}
        match={match}
        history={history}
        rightComponents={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              variant="outlined"
              className='bg-qiplus text-white'
              startIcon={<AddIcon />}
              onClick={() => setIsFormOpen(true)}
            >
              {langMessages['campaigns.hunter.new']}
            </Button>
            <ButtonGroup className={classes.viewToggle} variant="outlined">
              <Tooltip title={langMessages['campaigns.hunter.listView']}>
                <Button
                  className={viewMode === 'list' ? 'bg-qiplus text-white' : ''}
                  onClick={() => setViewMode('list')}
                >
                  <ListIcon />
                </Button>
              </Tooltip>
              <Tooltip title={langMessages['campaigns.hunter.gridView']}>
                <Button
                  className={viewMode === 'grid' ? 'bg-qiplus text-white' : ''}
                  onClick={() => setViewMode('grid')}
                >
                  <GridIcon />
                </Button>
              </Tooltip>
            </ButtonGroup>
          </div>
        }
      />
      <div className="dashboard-wrapper widgets-wrapper mb-4">
        <Container maxWidth="lg" style={{ paddingTop: '1rem', paddingBottom: '1rem' }}>
          {loadingPixels && (
            <Box flex={1} display="flex" justifyContent="center" alignItems="center">
              <CircularProgress />
            </Box>
          )}
          {viewMode === 'grid' && !loadingPixels && (
            <PixelGrid
              pixels={pixels}
              onEdit={handleEditPixel}
              onDelete={handleDeletePixel}
              onToggle={handleTogglePixel}
              onViewCode={handleViewCode}
              onClone={handleClonePixel}
              onStats={handleStats}
            />
          )}
          {viewMode === 'list' && !loadingPixels && (
            <PixelTable
              pixels={pixels}
              onEdit={handleEditPixel}
              onDelete={handleDeletePixel}
              onToggle={handleTogglePixel}
              onViewCode={handleViewCode}
              onClone={handleClonePixel}
              onStats={handleStats}
            />
          )}

          <Dialog
            open={isFormOpen}
            onClose={() => {
              setIsFormOpen(false);
              setSelectedPixel(null);
            }}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>
              {selectedPixel ? langMessages['campaigns.hunter.edit'] : langMessages['campaigns.hunter.create']}
            </DialogTitle>
            <DialogContent>
              <PixelForm
                pixel={selectedPixel}
                loading={creatingPixel}
                onSubmit={selectedPixel ? handleUpdatePixel : handleCreatePixel}
                onCancel={() => {
                  setIsFormOpen(false);
                  setSelectedPixel(null);
                }}
              />
            </DialogContent>
          </Dialog>

          <Dialog
            open={isCodeOpen}
            onClose={() => {
              setIsCodeOpen(false);
              setSelectedPixel(null);
            }}
            maxWidth="md"
            fullWidth
          >
            <DialogTitle>{langMessages['campaigns.hunter.code']}</DialogTitle>
            <DialogContent>
              {selectedPixel && (
                <PixelCode
                  pixel={selectedPixel}
                  onClose={() => {
                    setIsCodeOpen(false);
                    setSelectedPixel(null);
                  }}
                />
              )}
            </DialogContent>
          </Dialog>
        </Container>
      </div>
    </div>
  );
};

export default withRouter(PixelHunterPage);
