import { MOMENT_ISO } from "Constants";
import moment from "moment";

export class TimezoneUtil {
    static serverTimezone = 'America/Sao_Paulo';
    static userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    /**
     * Define um novo fuso horário para o servidor.
     * @param {string} timezone - Fuso horário a ser definido.
     */
    setTimezone(timezone) {
        this.userTimezone = timezone;
    }

    /**
     * Obtém a diferença em milissegundos entre dois fusos horários.
     * @param {string} fromTimezone - Fuso horário de origem.
     * @param {string} toTimezone - Fuso horário de destino.
     * @param {Date} [date=new Date()] - Data de referência para calcular a diferença.
     * @returns {number} - Diferença em milissegundos.
     */
    static getTimezoneOffset(fromTimezone, toTimezone, date = new Date()) {
        const fromTime = new Date(date.toLocaleString('en-US', { timeZone: fromTimezone }));
        const toTime = new Date(date.toLocaleString('en-US', { timeZone: toTimezone }));
        return toTime - fromTime;
    }

    /**
     * Ajusta para um formato de data e hora.
     * @param {string|Date} date - Data no formato ISO ou objeto Date.
     * @returns {String} - Data e hora formatadas.
     */
    static formatDate(date, format) {
        const formatForm = format ? format : MOMENT_ISO;
        return moment(date).format(formatForm);
    }

    /**
     * Converte uma data armazenada no servidor para o timezone do usuário.
     * @param {string|Date} serverDate - Data no formato ISO ou objeto Date do servidor.
     * @returns {Date} - Objeto Date ajustado para o timezone do usuário.
     */
    static convertToUserTimezone(serverDate, formatted = true) {
        const serverDateObj = new Date(serverDate);
        if (isNaN(serverDateObj)) {
            throw new Error('Data inválida fornecida para conversão.');
        }
        const offset = this.getTimezoneOffset(this.serverTimezone, this.userTimezone, serverDateObj);
        const date = new Date(serverDateObj.getTime() + offset);

        return formatted ? this.formatDate(date) : date;
    }

    /**
     * Converte uma data inserida pelo usuário para o timezone do servidor.
     * @param {string|Date} userDate - Data no formato ISO ou objeto Date do usuário.
     * @returns {Date} - Objeto Date ajustado para o timezone do servidor.
     */
    static convertToServerTimezone(userDate, formatted = false) {
        const userDateObj = new Date(userDate);
        if (isNaN(userDateObj)) {
            throw new Error('Data inválida fornecida para conversão.');
        }
        const offset = this.getTimezoneOffset(this.userTimezone, this.serverTimezone, userDateObj);
        const date = new Date(userDateObj.getTime() + offset);

        return formatted ? this.formatDate(date) : date;
    }

    static convertToTimestamp(date) {
        return new Date(date).getTime();
    }
}
