import { FirebaseRepository } from "FirebaseRef/repository";
import { langMessages } from 'Lang/index';
import React, { useEffect, useState } from 'react';
import { Redirect, useParams } from 'react-router-dom';

/**
 * Hook para proteger rotas que requerem autenticação
 * @param {Object} params - Parâmetros do hook
 * @param {Object} [params.path] - Path do documento
 * @param {string} [params.route='/'] - Rota para redirecionamento em caso de erro
 * @param {string} [params.children] - Rota contendo a tela
 */

export const ProtectedPageContext = React.createContext('')

export const ProtectedPage = (props) => {

    const { path, children, route, pathToVerify } = props

    const { id: pixelId } = useParams();

    const [doc, setDoc] = useState({})

    const [loading, setLoading] = useState(true)

    const [canUse, setCanUse] = useState(false)

    const [snackBarMessage, setSnackBarMessage] = useState(null)

    const repository = new FirebaseRepository()

    const account = JSON.parse(localStorage.getItem('account', false))

    const getDoc = (onSuccess, onError) => {
        return repository.getDoc(`${path.replace(":id", pixelId)}`, onSuccess, onError)
    }

    useEffect(() => {

        getDoc().then((doc) => {
            const verify = pathToVerify ? doc[pathToVerify].accountId : doc.accountId
            if (verify !== account.id) {
                setLoading(false)
            } else {
                setCanUse(true)
                setLoading(false)
                setDoc(doc)
            }

        }).catch((error) => {
            setLoading(false)
        })
    }, [])

    if (loading) {
        return (
            <div>Loading...</div>
        )
    } else if (!loading && !canUse) {
        return <Redirect to={{
            pathname: route,
            state: {
                toast: {
                    message: langMessages['alerts.notAccountOwner'],
                    severity: 'warning'
                }
            }
        }} />
    } else {
        return (
            <ProtectedPageContext.Provider
                value={{
                    doc
                }}
            >
                {children}
            </ProtectedPageContext.Provider>)
    }
};
