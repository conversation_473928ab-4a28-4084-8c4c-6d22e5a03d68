/**
 * GrapesJsWidget
 */
import React, { Component } from 'react';

// rct card box
import { Button, Snackbar } from '@material-ui/core';
import ShortcodesMap from 'Constants/ShortcodesMap';

// grapesjs
import { Alert } from '@material-ui/lab';
import { langMessages } from 'Lang/index';
import grapesjs from 'grapesjs';
import plugin from 'grapesjs-preset-newsletter';



let mapShortcodesTimeout

const ShortcodesToolbar = ({ selectedShortcodes, onClick }) => (
  <div className="qiplus-toolbar qiplus-shortcodes-toolbar bd-1 bg-white border-primary p-15">
    <div className="d-flex flex-wrap qiplus-toolbar-wrapper">
      {ShortcodesMap.map((shortCode, k) => {
        const { shortcode, label, shortcodeField, shortcodeTip } = shortCode
        return (
          <Button
            variant={selectedShortcodes.find(s => s === shortcode) ? 'contained' : 'outlined'}
            color="primary"
            key={k}
            onClick={e => onClick(shortcode)}
            value={shortcode}
            className="mr-5 mb-5 btn-xs"
          >
            {label || shortcodeField} {shortcodeTip && <small>{`(${shortcodeTip})`}</small>}
          </Button>
        )
      })}
    </div>
  </div>
)

class GrapesJsWidget extends Component {
  onChangeTimeout = null

  constructor(props) {
    super(props)
    this.state = {
      editorHtml: this.props.content || '',
      mountedEditor: false,
      height: window.innerHeight - 250,
      selectedShortcodes: [],
      snackBarMessage: (null)
    }
    this.wrapperRef = React.createRef()
    this.ckEditorRef = React.createRef()
    this.editorRef = null
    this.insertText = this.insertText.bind(this)
  }



  componentDidMount() {

    this.makeEditor()

    if (this.wrapperRef.current && this.wrapperRef.current.getClientRects) {
      const editorRects = this.wrapperRef.current.getClientRects()
      if (editorRects && editorRects[0] && editorRects[0].top) {
        const height = window.innerHeight - editorRects[0].top - 50
        this.setState({ height })
      }
    }
  }

  componentDidUpdate(prevProps) {

    if (!prevProps.content && this.props.content) {
      this.setState({ editorHtml: this.props.content })
    }
  }


  makeTemplete() {
    return this.props.content || `<body style="box-sizing: border-box; margin: 0;">
  <table id="ib8dlr" width="100%" height="150" style="box-sizing: border-box; height: 150px; margin: 0 auto 10px auto; padding: 5px 5px 5px 5px; width: 100%;">
    <tbody id="iu1w" style="box-sizing: border-box;">
      <tr id="iqgx" style="box-sizing: border-box;">
        <td id="is61" class="main-body-cell" style="box-sizing: border-box;">
          <table width="90%" height="0" id="ixii" bgcolor="#f1f1f1" class="container" style="background-color: #f1f1f1; box-sizing: border-box; font-family: Helvetica, serif; min-height: 150px; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; margin-top: auto; margin-right: auto; margin-bottom: auto; margin-left: auto; height: 0px; width: 90%; max-width: 550px;">
            <tbody id="ik64" style="box-sizing: border-box;">
              <tr id="i9bb" style="box-sizing: border-box;">
                <td valign="top" id="i2a1" bgcolor="#f6f6f6" class="container-cell" style="background-color: #f6f6f6; box-sizing: border-box; vertical-align: top; font-size: medium; padding-bottom: 50px;">
                  <table width="100%" height="0" id="i50d" class="table100 c1790" style="box-sizing: border-box; width: 100%; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; height: 0px; min-height: 30px; border-collapse: separate; margin-top: 0px; margin-right: 0px; margin-bottom: 10px; margin-left: 0px;">
                    <tbody id="i9kb6" style="box-sizing: border-box;">
                      <tr id="isl4q" style="box-sizing: border-box;">
                        <td id="c1793" align="right" class="top-cell" style="box-sizing: border-box; text-align: right; color: rgb(152, 156, 165);">
                          <u id="c307" class="browser-link" style="box-sizing: border-box; font-size: 12px;">View in browser
                          </u>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table width="100%" id="i0ik1" class="c1766" style="box-sizing: border-box; margin-top: 0px; margin-right: auto; margin-bottom: 10px; margin-left: 0px; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; width: 100%; min-height: 30px;">
                    <tbody id="i2fxc" style="box-sizing: border-box;">
                      <tr id="ibdtl" style="box-sizing: border-box;">
                        <td width="11%" id="io7wz" class="cell c1769" style="box-sizing: border-box; width: 11%;">
                          <img src="https://qiplus.com.br/wp-content/uploads/LOGO_QI_PLUS_FINAL-4.png" alt="GrapesJS." id="i94dl" class="c926" style="box-sizing: border-box; color: rgb(158, 83, 129); width: 100%; font-size: 50px;">
                        </td>
                        <td width="70%" valign="middle" id="isb23" class="cell c1776" style="box-sizing: border-box; width: 70%; vertical-align: middle;">
                          <div id="is7g8" class="c1144" style="box-sizing: border-box; padding-top: 10px; padding-right: 10px; padding-bottom: 10px; padding-left: 10px; font-size: 17px; font-weight: 300;">QiPlus Newsletter Builder
                            <br id="isbae" draggable="true" data-highlightable="1" style="box-sizing: border-box;">
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table height="0" id="ivgik" class="card" style="box-sizing: border-box; min-height: 150px; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; margin-bottom: 20px; height: 0px;">
                    <tbody id="ioi8x" style="box-sizing: border-box;">
                      <tr id="iaqpj" style="box-sizing: border-box;">
                        <td bgcolor="rgb(255, 255, 255)" align="center" id="ijl6j" class="card-cell" style="box-sizing: border-box; background-color: rgb(255, 255, 255); overflow-x: hidden; overflow-y: hidden; border-top-left-radius: 3px; border-top-right-radius: 3px; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; text-align: center;">
                          <img src="https://qiplus.com.br/wp-content/uploads/trafego.jpg" alt="Big image here" id="in6fd" class="c1271" style="box-sizing: border-box; width: 100%; margin-top: 0px; margin-right: 0px; margin-bottom: 15px; margin-left: 0px; font-size: 50px; color: rgb(120, 197, 214); line-height: 250px; text-align: center;">
                          <table width="100%" height="0" id="iowsf" class="table100 c1357" style="box-sizing: border-box; width: 100%; min-height: 150px; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; height: 0px; margin-top: 0px; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; border-collapse: collapse;">
                            <tbody id="ielgv" style="box-sizing: border-box;">
                              <tr id="ij7tj" style="box-sizing: border-box;">
                                <td valign="top" id="i584v" class="card-content" style="box-sizing: border-box; font-size: 13px; line-height: 20px; color: rgb(111, 119, 125); padding-top: 10px; padding-right: 20px; padding-bottom: 0px; padding-left: 20px; vertical-align: top;">
                                  <h1 id="iqyvw" class="card-title" style="box-sizing: border-box; font-size: 25px; font-weight: 300; color: rgb(68, 68, 68);">[::qi-firstName::] [::qi-lastName::] Construa sua Newsletter mais rápido do que nunca
                                    <br id="il9lo" draggable="true" data-highlightable="1" style="box-sizing: border-box;">
                                  </h1>
                                  <p id="i90t2" class="card-text" style="box-sizing: border-box;">Importe, crie, teste e exporte modelos de boletins informativos responsivos mais rápido do que nunca usando o QiPlus Newsletter Builder. asdf
                                  </p>
                                  <table width="100%" id="iv53v" class="c1542" style="box-sizing: border-box; margin-top: 0px; margin-right: auto; margin-bottom: 10px; margin-left: auto; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; width: 100%;">
                                    <tbody id="idkvt" style="box-sizing: border-box;">
                                      <tr id="iuwbl" style="box-sizing: border-box;">
                                        <td id="c1545" align="center" class="card-footer" style="box-sizing: border-box; padding-top: 20px; padding-right: 0px; padding-bottom: 20px; padding-left: 0px; text-align: center;">
                                          <a href="https://qiplus.com.br/" id="i30eh" class="button" style="box-sizing: border-box; font-size: 12px; padding-top: 10px; padding-right: 20px; padding-bottom: 10px; padding-left: 20px; background-color: rgb(217, 131, 166); color: rgb(255, 255, 255); text-align: center; border-top-left-radius: 3px; border-top-right-radius: 3px; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px; font-weight: 300;">Experimente</a>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table width="100%" id="ihr3y" class="list-item" style="box-sizing: border-box; height: auto; width: 100%; margin-top: 0px; margin-right: auto; margin-bottom: 10px; margin-left: auto; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px;">
                    <tbody id="izx7h" style="box-sizing: border-box;">
                      <tr id="i6lca" style="box-sizing: border-box;">
                        <td bgcolor="rgb(255, 255, 255)" id="iz5pf" class="list-item-cell" style="box-sizing: border-box; background-color: rgb(255, 255, 255); border-top-left-radius: 3px; border-top-right-radius: 3px; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px; overflow-x: hidden; overflow-y: hidden; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px;">
                          <table width="100%" height="150" id="i6j04" class="list-item-content" style="box-sizing: border-box; border-collapse: collapse; margin-top: 0px; margin-right: auto; margin-bottom: 0px; margin-left: auto; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; height: 150px; width: 100%;">
                            <tbody id="imj33" style="box-sizing: border-box;">
                              <tr id="ip9jg" class="list-item-row" style="box-sizing: border-box;">
                                <td width="30%" id="i98qk" class="list-cell-left" style="box-sizing: border-box; width: 30%; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px;">
                                  <img src="data:image/png;base64,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" alt="Image1" id="ie9h8" class="list-item-image" style="box-sizing: border-box; color: rgb(217, 131, 166); font-size: 45px; width: 100%;">
                                </td>
                                <td width="70%" id="ih9bi" class="list-cell-right" style="box-sizing: border-box; width: 70%; color: rgb(111, 119, 125); font-size: 13px; line-height: 20px; padding-top: 10px; padding-right: 20px; padding-bottom: 0px; padding-left: 20px;">
                                  <h1 id="ia1p4" class="card-title" style="box-sizing: border-box; font-size: 25px; font-weight: 300; color: rgb(68, 68, 68);">Blocos integrados
                                  </h1>
                                  <p id="iuueo" class="card-text" style="box-sizing: border-box;">Arraste e solte blocos integrados do painel direito e estilize-os em questão de segundos
                                  </p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table width="100%" id="idtzr" class="list-item" style="box-sizing: border-box; height: auto; width: 100%; margin-top: 0px; margin-right: auto; margin-bottom: 10px; margin-left: auto; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px;">
                    <tbody id="ih7sm" style="box-sizing: border-box;">
                      <tr id="inqvh" style="box-sizing: border-box;">
                        <td bgcolor="rgb(255, 255, 255)" id="iy2vo" class="list-item-cell" style="box-sizing: border-box; background-color: rgb(255, 255, 255); border-top-left-radius: 3px; border-top-right-radius: 3px; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px; overflow-x: hidden; overflow-y: hidden; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px;">
                          <table width="100%" height="150" id="i18uv" class="list-item-content" style="box-sizing: border-box; border-collapse: collapse; margin-top: 0px; margin-right: auto; margin-bottom: 0px; margin-left: auto; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px; height: 150px; width: 100%;">
                            <tbody id="i5psc" style="box-sizing: border-box;">
                              <tr id="iqhbl" class="list-item-row" style="box-sizing: border-box;">
                                <td width="30%" id="ixi3h" class="list-cell-left" style="box-sizing: border-box; width: 30%; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px;">
                                  <img src="https://placehold.co/150x150/blue/white/png?text=QiPlus" alt="Image2" id="id2mk" class="list-item-image" style="box-sizing: border-box; color: rgb(217, 131, 166); font-size: 45px; width: 100%;">
                                </td>
                                <td width="70%" id="ih2iu" class="list-cell-right" style="box-sizing: border-box; width: 70%; color: rgb(111, 119, 125); font-size: 13px; line-height: 20px; padding-top: 10px; padding-right: 20px; padding-bottom: 0px; padding-left: 20px;">
                                  <h1 id="i17sw" class="card-title" style="box-sizing: border-box; font-size: 25px; font-weight: 300; color: rgb(68, 68, 68);">Troque as Imagens
                                  </h1>
                                  <p id="i4tno" class="card-text" style="box-sizing: border-box;">Crie um boletim informativo bonito, mesmo sem imagens habilitadas pelos clientes de e-mail
                                  </p>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table width="100%" id="i0gcf" class="grid-item-row" style="box-sizing: border-box; margin-top: 0px; margin-right: auto; margin-bottom: 10px; margin-left: auto; padding-top: 5px; padding-right: 0px; padding-bottom: 5px; padding-left: 0px; width: 100%;">
                    <tbody id="ivkyr" style="box-sizing: border-box;">
                      <tr id="iamdk" style="box-sizing: border-box;">
                        <td width="50%" valign="top" id="ih9ir" class="grid-item-cell2-l" style="box-sizing: border-box; vertical-align: top; padding-right: 10px; width: 50%;">
                          <table width="100%" id="ipauh" class="grid-item-card" style="box-sizing: border-box; width: 100%; padding-top: 5px; padding-right: 0px; padding-bottom: 5px; padding-left: 0px; margin-bottom: 10px;">
                            <tbody id="icefd" style="box-sizing: border-box;">
                              <tr id="im5lk" style="box-sizing: border-box;">
                                <td bgcolor="rgb(255, 255, 255)" align="center" id="iengl" class="grid-item-card-cell" style="box-sizing: border-box; background-color: rgb(255, 255, 255); overflow-x: hidden; overflow-y: hidden; border-top-left-radius: 3px; border-top-right-radius: 3px; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px; text-align: center; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px;">
                                  <img src="data:image/png;base64,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" alt="Image1" id="ia005" class="grid-item-image" style="box-sizing: border-box; line-height: 150px; font-size: 50px; color: rgb(120, 197, 214); margin-bottom: 15px; width: 100%;">
                                  <table id="i3i01" class="grid-item-card-body" style="box-sizing: border-box;">
                                    <tbody id="ia3rv" style="box-sizing: border-box;">
                                      <tr id="ipu8h" style="box-sizing: border-box;">
                                        <td width="100%" id="iadyn" class="grid-item-card-content" style="box-sizing: border-box; font-size: 13px; color: rgb(111, 119, 125); padding-top: 0px; padding-right: 10px; padding-bottom: 20px; padding-left: 10px; width: 100%; line-height: 20px;">
                                          <h1 id="i487m" class="card-title" style="box-sizing: border-box; font-size: 25px; font-weight: 300; color: rgb(68, 68, 68);">Variáveis
                                          </h1>
                                          <p id="ipl1o" class="card-text" style="box-sizing: border-box;">Você pode usar as variáveis do sistema para deixar o seu email, mais pessoal. Para usar click no variável que deseja utilizar nas opções ao lado que ela ira para área de Transferencia, pois click no bloco que desejar e seleciona CTRL+V no seu teclado. [::qi-firstName::]
                                          </p>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                        <td width="50%" valign="top" id="itmwv" class="grid-item-cell2-r" style="box-sizing: border-box; vertical-align: top; padding-left: 10px; width: 50%;">
                          <table width="100%" id="i1v0b" class="grid-item-card" style="box-sizing: border-box; width: 100%; padding-top: 5px; padding-right: 0px; padding-bottom: 5px; padding-left: 0px; margin-bottom: 10px;">
                            <tbody id="ibaxr" style="box-sizing: border-box;">
                              <tr id="iywiu" style="box-sizing: border-box;">
                                <td bgcolor="rgb(255, 255, 255)" align="center" id="id2vy" class="grid-item-card-cell" style="box-sizing: border-box; background-color: rgb(255, 255, 255); overflow-x: hidden; overflow-y: hidden; border-top-left-radius: 3px; border-top-right-radius: 3px; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px; text-align: center; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px;">
                                  <img src="https://placehold.co/250x250/blue/white/png?text=QiPlus" alt="Image2" id="ircmd" class="grid-item-image" style="box-sizing: border-box; line-height: 150px; font-size: 50px; color: rgb(120, 197, 214); margin-bottom: 15px; width: 100%;">
                                  <table id="i83kz" class="grid-item-card-body" style="box-sizing: border-box;">
                                    <tbody id="im3a1" style="box-sizing: border-box;">
                                      <tr id="i4xj5" style="box-sizing: border-box;">
                                        <td width="100%" id="igjdh" class="grid-item-card-content" style="box-sizing: border-box; font-size: 13px; color: rgb(111, 119, 125); padding-top: 0px; padding-right: 10px; padding-bottom: 20px; padding-left: 10px; width: 100%; line-height: 20px;">
                                          <h1 id="ikmtf" class="card-title" style="box-sizing: border-box; font-size: 25px; font-weight: 300; color: rgb(68, 68, 68);">Responsivo
                                          </h1>
                                          <p id="iptrxp" class="card-text" style="box-sizing: border-box;">Usando o gerenciador de dispositivos você sempre enviará um conteúdo totalmente responsivo
                                          </p>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table align="center" id="iezc4c" class="footer" style="box-sizing: border-box; margin-top: 50px; color: rgb(152, 156, 165); text-align: center; font-size: 11px; padding-top: 5px; padding-right: 5px; padding-bottom: 5px; padding-left: 5px;">
                    <tbody id="iqshvl" style="box-sizing: border-box;">
                      <tr id="ik5zqx" style="box-sizing: border-box;">
                        <td id="i4nsa6" class="footer-cell" style="box-sizing: border-box;">
                          <div id="iv5xck" class="c2577" style="box-sizing: border-box; padding-top: 10px; padding-right: 10px; padding-bottom: 10px; padding-left: 10px;">
                            <p id="ipppqm" class="footer-info" style="box-sizing: border-box;">QiPlus Newsletter Builder faz parte da plataforma QiPlus. Para mais informações sobre nós entre em contato com a QiPlus, ou através do nosso suporte.
                            </p>
                            <p id="iudr9h" style="box-sizing: border-box;">
                              <a href id="ijl5af" class="link" style="box-sizing: border-box; color: rgb(217, 131, 166);">QiPlus Newsletter </a>
                              <br id="ir3col" style="box-sizing: border-box;">
                            </p>
                          </div>
                          <div id="i8g35m" class="c2421" style="box-sizing: border-box; padding-top: 10px; padding-right: 10px; padding-bottom: 10px; padding-left: 10px;">
                            MADE BY
                            <a href id="i643eb" class="link" style="box-sizing: border-box; color: rgb(217, 131, 166);">QiPlus</a>
                            <p id="iiiczr" style="box-sizing: border-box;">
                            </p>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
        </td>
        <td id="icghlh" valign="top" style="box-sizing: border-box; padding: 0; margin: 0; vertical-align: top;">
        </td>
      </tr>
    </tbody>
  </table>
</body>`

  }



  makeEditor() {
    let editor = grapesjs.init({
      container: '#gjs',
      plugins: [plugin],

      storageManager: {
        type: 'local', // Storage type. Available: local | remote
        autosave: true, // Store data automatically
        autoload: false, // Autoload stored data on init
        stepsBeforeSave: 1,
        options: {
          local: { key: `gjsProject-1` }
        }
      },
      components: this.makeTemplete(),
      pluginsOpts: {
        plugin: {

        },
      },


    });

    editor.on('storage:end', (type) => {

      const htmlWithCss = editor.runCommand('gjs-get-inlined-html');

      // const pagesHtml = editor.Pages.getAll().map((page) => {
      //   const component = page.getMainComponent();

      //   const html = editor.getHtml({ component });
      //   const css = editor.getCss({ component });

      //   return (`
      //     <style>${css}</style>
      //     ${html}
      //   `)

      // });

      this.handleChange(htmlWithCss)
      return htmlWithCss

    });
    return editor
  }

  async insertText(text) {
    try {
      await navigator.clipboard.writeText(text);
      this.setState({
        snackBarMessage: {
          message: langMessages['widgets.mailing.grapesjs.copy'].replace('[%val]', text), //langMessages['shotx.snipers.form.withoutTitle'],
          severity: 'info',
        }
      })

    } catch (err) {
      console.log('Falha ao copiar o texto', err);
    }


  }

  handleChange(e) {

    this.props.onChange(e)

  }

  getContentHeight() {
    if (this.state.height) {
      return this.state.height
    }
    return window.innerHeight - 250
  }


  renderEditor() {
    return (
      <div id='gjs' className="editor-wrapper ckeditor-wrapper" />
    )
  }

  renderWrapper() {
    const EditorWrapper = this.props.editorWrapper

    return (EditorWrapper && <EditorWrapper>{this.renderEditor()}</EditorWrapper>) || this.renderEditor()
  }

  getContent() {
    return (this.editorRef || {}).getData ? this.editorRef.getData() : this.state.editorHtml
  }

  renderShortcodes() {
    const ShortCodeWrapper = this.props.shortCodeWrapper
    const { selectedShortcodes } = this.state

    return (
      (ShortCodeWrapper && <ShortCodeWrapper shortcodesMap={ShortcodesMap} selectedShortcodes={selectedShortcodes} onClick={this.insertText} />) || (
        <ShortcodesToolbar selectedShortcodes={selectedShortcodes} onClick={this.insertText} />
      )
    )
  }

  parentWrapper(props) {
    return <React.Fragment>{props.children}</React.Fragment>
  }

  render() {
    const ParentWrapper = this.props.parentWrapper || this.parentWrapper
    const { snackBarMessage } = this.state

    return (
      <>
        <div>
          <Snackbar
            anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
            open={!!snackBarMessage}
            onClose={() => this.setState({ snackBarMessage: (null) })}
            autoHideDuration={5000}
          >
            <Alert onClose={() => this.setState({ snackBarMessage: (null) })} severity={snackBarMessage?.severity}>
              {snackBarMessage?.message}
            </Alert>
          </Snackbar>
        </div>
        <ParentWrapper>
          {this.renderShortcodes()}
          {this.renderWrapper()}
        </ParentWrapper>
      </>
    )
  }
}

export default GrapesJsWidget
