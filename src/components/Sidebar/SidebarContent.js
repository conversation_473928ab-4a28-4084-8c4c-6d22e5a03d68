/**
 * Sidebar Content
 */
import List from '@material-ui/core/List'
import ListSubheader from '@material-ui/core/ListSubheader'
import React, { Component } from 'react'
import { connect } from 'react-redux'
import { <PERSON>, withRouter } from 'react-router-dom'

import AppModules from '../../constants/AppModules'

import COLLECTIONS from '../../constants/AppCollections'

import IntlMessages from 'Util/IntlMessages'

import NavMenuItem from './NavMenuItem'

//Helper
import { currentUserCan } from 'Helpers/helpers'

// redux actions
import { onToggleMenu } from 'Actions'
import Scrollbars from 'react-custom-scrollbars'
import { Badge, Nav, NavItem, NavLink } from 'reactstrap'
import { areEqualObjects } from '../../helpers/helpers'

class SidebarContent extends Component {
  state = {
    modules: this.props.account.modules || {},
  }

  componentDidUpdate(prevProps, prevState) {
    const {
      account: { modules },
    } = this.props
    if (!areEqualObjects(modules, prevState.modules)) {
      this.setState({ modules })
    }
  }

  toggleMenu(menu, stateCategory) {
    let data = {
      menu,
      stateCategory,
    }
    this.props.onToggleMenu(data)
  }

  render() {
    const {
      history,
      user,
      account,
      account: { modules },
      navLinks,
      chatMessages,
      deskNotifications,
      toggleDeskNotifications,
    } = this.props
    const newNotifications = deskNotifications.filter(n => !n.viewed)
    const unreadChatMessages = Object.keys(chatMessages).reduce((unread, cId) => {
      return [...unread, ...chatMessages[cId].filter(m => m.from === cId && !m.read)]
    }, [])
    const navGroups = navLinks
    Object.keys(navGroups).forEach(menuKey => {
      let navGroup = navGroups[menuKey]
      navGroups[menuKey] = navGroup.filter((nav, n) => {
        if (Array.isArray(nav.child_routes)) {
          if (account.parentId && nav.child_routes) {
            nav.child_routes = nav.child_routes.filter(nav => nav.collection !== 'treebusiness')
          }
          navGroups[menuKey][n].child_routes = nav.child_routes.filter((child, c) => {
            const { module, collection } = child
            if (child.child_routes) {
              navGroups[menuKey][n].child_routes[c].child_routes = child.child_routes.filter(granchild => {
                let granchildCap = granchild.cap || child.cap || 'edit'
                let r = currentUserCan(granchildCap, collection, user, account)
                return r
              })
            }
            let childCap = child.cap || 'list'
            let r = currentUserCan(childCap, collection, user, account)
            return r
          })
        }
        let result =
          (!navGroups[menuKey][n].child_routes || navGroups[menuKey][n].child_routes.length) &&
          (!nav.module || !(nav.module in (modules || {})) || (modules || {})[nav.module]) &&
          (!nav.menu_level || nav.menu_level >= (user || {}).level)
        return result
      })
    })

    const MailboxesModule = AppModules[COLLECTIONS.MAILBOXES_COLLECTION_NAME]
    const ChatAppModule = AppModules[COLLECTIONS.CHATS_COLLECTION_NAME]
    const TasklistsModule = AppModules[COLLECTIONS.TASKLISTS_COLLECTION_NAME]
    const CalendarModule = AppModules[COLLECTIONS.CALENDAR_COLLECTION_NAME]

    return (
      <div className="rct-sidebar-wrap">
        <Scrollbars
          className="rct-scroll"
          autoHide={false}
          thumbSize={80}
          renderTrackVertical={props => <div {...props} className="track-vertical" />}
          renderThumbVertical={props => <div {...props} className="thumb-vertical" />}
        >
          <div className="rct-sidebar-nav">
            <nav className="navigation">
              {Object.keys(navGroups).map(
                (menuKey, menuIndex) =>
                  !!navGroups[menuKey].length && (
                    <List
                      className="rct-mainMenu p-0 m-0 list-unstyled"
                      subheader={
                        (navGroups[menuKey].subheader && (
                          <ListSubheader className="side-title" component="li">
                            <IntlMessages id={navGroups[menuKey].subheader} />
                          </ListSubheader>
                        )) ||
                        null
                      }
                      key={menuIndex}
                    >
                      {navGroups[menuKey].map((menu, key) => (
                        <NavMenuItem location={this.props.location} menu={menu} key={key} onToggleMenu={() => this.toggleMenu(menu, menuKey)} />
                      ))}
                    </List>
                  )
              )}
            </nav>
            <nav className="app-navigation">
              <Nav className="custom-tabs flex-column">
                <NavItem>
                  <NavLink className="position-relative" onClick={e => toggleDeskNotifications()}>
                    {!!newNotifications.length && (
                      <Badge color="danger" className="badge-xs badge-top-right rct-notify">
                        {newNotifications.length}
                      </Badge>
                    )}
                    <i className={'zmdi zmdi-notifications'} />
                    <IntlMessages id={'sidebar.alerts'} />
                  </NavLink>
                </NavItem>
                {currentUserCan('list', ChatAppModule.collection) && (
                  <NavItem>
                    <NavLink className="position-relative" onClick={e => history.push(`/${ChatAppModule.route}`)}>
                      {!!unreadChatMessages.length && (
                        <Badge color="danger" className="badge-xs badge-top-right rct-notify">
                          {unreadChatMessages.length}
                        </Badge>
                      )}
                      <i className={ChatAppModule.icon} />
                      <IntlMessages id={`sidebar.${ChatAppModule.collection}`} />
                    </NavLink>
                  </NavItem>
                )}
                {currentUserCan('list', MailboxesModule.collection) && (
                  <NavItem>
                    <Link className="nav-link" to={`/${MailboxesModule.route}`}>
                      <i className={MailboxesModule.icon} />
                      <IntlMessages id={`sidebar.${MailboxesModule.collection}`} />
                    </Link>
                  </NavItem>
                )}
                <NavItem>
                  <Link className="nav-link" to={`/${TasklistsModule.route}`}>
                    <i className={TasklistsModule.icon} />
                    <IntlMessages id={`sidebar.${TasklistsModule.collection}`} />
                  </Link>
                </NavItem>
                <NavItem>
                  <Link className="nav-link" to={`/${CalendarModule.route}`}>
                    <i className={CalendarModule.icon} />
                    <IntlMessages id={`sidebar.${CalendarModule.collection}`} />
                  </Link>
                </NavItem>
              </Nav>
            </nav>
          </div>
        </Scrollbars>
      </div>
    )
  }
}

// map state to props
const mapStateToProps = ({ sidebarReducer: { navLinks }, chatAppReducer: { chatMessages }, authReducer: { ownerId, user, account } }) => {
  return { ownerId, user, account, navLinks, chatMessages }
}

export default withRouter(connect(mapStateToProps, { onToggleMenu })(SidebarContent))
