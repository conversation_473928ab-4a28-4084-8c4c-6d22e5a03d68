/**
 * Reactify Sidebar
 */
import classNames from 'classnames'
import $ from 'jquery'
import React, { Component, Fragment } from 'react'
import { connect } from 'react-redux'
import { Link, withRouter } from 'react-router-dom'

// redux actions
import { collapsedSidebarAction, miniSidebarAction, onToggleDeskNotifications, updateDeskNotifications } from 'Actions'

// components
import SidebarContent from './SidebarContent'


class Sidebar extends Component {
  state = {
    windowWidth: $(window).width(),
    windowHeight: $(window).height(),
  }

  shouldComponentUpdate(nextProps) {
    const { enableSidebarBackgroundImage, selectedSidebarImage, isDarkSidenav, locale } = this.props
    if (
      enableSidebarBackgroundImage !== nextProps.enableSidebarBackgroundImage ||
      selectedSidebarImage !== nextProps.selectedSidebarImage ||
      isDarkSidenav !== nextProps.isDarkSidenav ||
      locale
    ) {
      return true
    } else {
      return false
    }
  }

  componentDidUpdate() {
    let siteLogoHeight = $('.site-logo').outerHeight()
    let sidebarUserHeight = $('.sidebar-user').outerHeight()
    let appNavigationHeight = $('.app-navigation').outerHeight()

    $('.rct-sidebar-wrap > .rct-scroll').css({
      minHeight: window.innerHeight - (siteLogoHeight + sidebarUserHeight + appNavigationHeight + 2),
      maxHeight: window.innerHeight - (siteLogoHeight + sidebarUserHeight + appNavigationHeight + 2),
    })
  }

  componentDidMount() {
    this.updateDimensions()
    const { windowWidth } = this.state
    if (windowWidth <= 992) {
      this.props.miniSidebarAction(true)
      $('body').addClass('mini-sidebar')
    } else {
      $(document).on('mouseenter', 'body.mini-sidebar .rct-sidebar', function () {
        $('body').addClass('mini-sidebar-hover')
      })
      $(document).on('mouseleave', '.rct-sidebar', function () {
        $('body').removeClass('mini-sidebar-hover')
      })
    }
    window.addEventListener('resize', this.updateDimensions)
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.updateDimensions)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { windowWidth } = this.state
    const { collapsedSidebar } = this.props
    if (nextProps.location !== this.props.location) {
      if (windowWidth <= 1199) {
        this.props.collapsedSidebarAction(false)
      }
    }
  }

  onToggleMiniSidebar = event => {
    const { windowWidth } = this.state
    if (windowWidth <= 992) {
      return
    }
    setTimeout(() => {
      this.props.miniSidebarAction(!this.props.miniSidebar)
    }, 100)
  }

  updateDimensions = () => {
    this.setState({ windowWidth: $(window).width(), windowHeight: $(window).height() })
  }

  render() {
    const {
      enableSidebarBackgroundImage,
      selectedSidebarImage,
      isDarkSidenav,
      onToggleDeskNotifications,
      updateDeskNotifications,
      notificationsToggle,
      deskNotifications,
    } = this.props
    return (
      <Fragment>
        <div
          className={classNames('rct-sidebar', { 'background-none': !enableSidebarBackgroundImage })}
          style={{ backgroundImage: enableSidebarBackgroundImage ? `url(${selectedSidebarImage})` : 'none' }}
        >
          <div className={classNames('rct-sidebar-content', { 'sidebar-overlay-dark': isDarkSidenav, 'sidebar-overlay-light': !isDarkSidenav })}>
            <div className="site-logo">
              <Link to="/" className="logo-mini">
                <img src={require('Assets/img/logo-color-icon.png')} className="mr-10" alt="site logo" width="45" />
              </Link>
              <Link to="/" className="logo-normal">
                <img src={require('Assets/img/logo-color-text.png')} className="img-fluid" alt="site-logo" width="120" />
              </Link>
              <span className="sidebar-collapse-btn d-none d-lg-flex" onClick={e => this.onToggleMiniSidebar(e)}>
                <i className={`${!this.props.miniSidebar ? 'ti-pin2' : 'ti-pin-alt'}`}></i>
              </span>
            </div>
            <SidebarContent
              toggleDeskNotifications={e => {
                if (!notificationsToggle) {
                  // if will be visible
                  updateDeskNotifications(
                    deskNotifications.filter(n => !n.viewed),
                    { viewed: true }
                  )
                }
                onToggleDeskNotifications(!notificationsToggle)
              }}
              deskNotifications={deskNotifications}
            />
          </div>
        </div>
      </Fragment>
    )
  }
}

// map state to props
const mapStateToProps = ({ settings, sidebarReducer }) => {
  const { enableSidebarBackgroundImage, selectedSidebarImage, collapsedSidebar, isDarkSidenav, locale, miniSidebar } = settings
  const { deskNotifications, notificationsToggle } = sidebarReducer
  return {
    enableSidebarBackgroundImage,
    selectedSidebarImage,
    collapsedSidebar,
    isDarkSidenav,
    locale,
    miniSidebar,
    deskNotifications,
    notificationsToggle,
  }
}

export default withRouter(
  connect(mapStateToProps, {
    collapsedSidebarAction,
    miniSidebarAction,
    onToggleDeskNotifications,
    updateDeskNotifications,
  })(Sidebar)
)
