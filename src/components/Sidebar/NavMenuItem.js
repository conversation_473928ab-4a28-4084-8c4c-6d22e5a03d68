/**
 * Nav Menu Item
 */
import classNames from 'classnames'
import React, { Component, Fragment } from 'react'
import { NavLink } from 'react-router-dom'

import { Chip, Collapse, List, ListItem, ListItemIcon, ListSubheader } from '@material-ui/core'

// intl messages
import IntlMessages from 'Util/IntlMessages'
import { isCurrentMenu } from '../../helpers/helpers'

class NavMenuItem extends Component {
  state = {
    subMenuOpen: '',
  }

  /**
   * On Toggle Collapse Menu
   */
  onToggleCollapseMenu(index) {
    if (this.state.subMenuOpen === '') {
      this.setState({
        subMenuOpen: index,
      })
    } else if (this.state.subMenuOpen !== index) {
      this.setState({
        subMenuOpen: index,
      })
    } else {
      this.setState({ subMenuOpen: '' })
    }
  }

  isCurrent(menu) {
    const { pathname } = this.props.location
    return isCurrentMenu(menu, pathname)
  }

  render() {
    const { menu, onToggleMenu } = this.props
    const { subMenuOpen } = this.state

    if (menu.child_routes != null) {
      return (
        <Fragment>
          <ListItem
            button
            component="li"
            onClick={onToggleMenu}
            className={`list-item ${classNames({ 'item-active': menu.open, 'item-multi': menu.type_multi, 'item-current': this.isCurrent(menu) })}`}
          >
            <ListItemIcon className="menu-icon">
              <i className={menu.menu_icon}></i>
            </ListItemIcon>
            <span className="menu text-capitalize">
              <IntlMessages id={menu.menu_title} />
            </span>
            {menu.new_item && menu.new_item === true ? <Chip label="new" className="new-item" color="secondary" /> : ''}
          </ListItem>
          <Collapse in={menu.open} timeout="auto" className="sub-menu">
            <Fragment>
              {menu.type_multi == null ? (
                <List
                  className="list-unstyled py-0 not-multi"
                  subheader={
                    (menu.subheader && (
                      <ListSubheader className="side-title" component="li">
                        <IntlMessages id={menu.subheader} />
                      </ListSubheader>
                    )) ||
                    null
                  }
                >
                  {menu.child_routes.map((subMenu, index) => {
                    return (
                      <ListItem button component="li" key={index}>
                        {subMenu.redirect ?
                          <a href={subMenu.path} target="_blank">
                            <span className="menu">
                              <IntlMessages id={subMenu.menu_title} />
                            </span>
                            {subMenu.new_item && subMenu.new_item === true ? <Chip label="new" className="new-item" color="secondary" /> : ''}
                          </a>
                          :
                          <NavLink to={subMenu.path} activeClassName="item-active">
                            <span className="menu">
                              <IntlMessages id={subMenu.menu_title} />
                            </span>
                            {subMenu.new_item && subMenu.new_item === true ? <Chip label="new" className="new-item" color="secondary" /> : ''}
                          </NavLink>}
                      </ListItem>
                    )
                  })}
                </List>
              ) : (
                <List
                  className="list-unstyled py-0 type-multi"
                  subheader={
                    (menu.subheader && (
                      <ListSubheader className="side-title" component="li">
                        <IntlMessages id={menu.subheader} />
                      </ListSubheader>
                    )) ||
                    null
                  }
                >
                  {menu.child_routes.map((subMenu, index) => {
                    return subMenu.child_routes ? (
                      <Fragment key={index}>
                        <ListItem
                          button
                          component="li"
                          className={`list-item nested-item ${classNames({ 'item-active': subMenuOpen === index, 'item-current': this.isCurrent(subMenu) })}`}
                          onClick={() => this.onToggleCollapseMenu(index)}
                        >
                          {subMenu.menu_icon && (
                            <ListItemIcon className="menu-icon">
                              <i className={subMenu.menu_icon}></i>
                            </ListItemIcon>
                          )}
                          <span className="menu">
                            <IntlMessages id={subMenu.menu_title} />
                          </span>
                        </ListItem>
                        <Collapse in={subMenuOpen === index} timeout="auto">
                          <List
                            className="list-unstyled py-0 multi-submenu"
                            subheader={
                              (subMenu.subheader && (
                                <ListSubheader className="side-title" component="li">
                                  <IntlMessages id={subMenu.subheader} />
                                </ListSubheader>
                              )) ||
                              null
                            }
                          >
                            {subMenu.child_routes.map((nestedMenu, nestedKey) => (
                              <ListItem button component="li" key={nestedKey}>
                                {nestedMenu.redirect ?
                                  <a href={nestedMenu.path} target="_blank">
                                    <span className="menu pl-10 d-inline-block">
                                      <IntlMessages id={nestedMenu.menu_title} />
                                    </span>
                                    {nestedMenu.new_item && nestedMenu.new_item === true ? <Chip label="new" className="new-item" color="secondary" /> : ''}
                                  </a>
                                  : <NavLink activeClassName="item-active" to={nestedMenu.path}>
                                    <span className="menu pl-10 d-inline-block">
                                      <IntlMessages id={nestedMenu.menu_title} />
                                    </span>
                                  </NavLink>}
                              </ListItem>
                            ))}
                          </List>
                        </Collapse>
                      </Fragment>
                    ) : (
                      <Fragment key={index}>
                        <ListItem
                          button
                          component="li"
                          className={`submenu-navlink ${classNames({ 'item-active': subMenuOpen === index, 'item-current': this.isCurrent(subMenu) })}`}
                        >
                          <NavLink to={subMenu.path} activeClassName="item-active">
                            {subMenu.menu_icon && (
                              <ListItemIcon className="menu-icon">
                                <i className={subMenu.menu_icon}></i>
                              </ListItemIcon>
                            )}
                            <span className="menu">
                              <IntlMessages id={subMenu.menu_title} />
                            </span>
                            {subMenu.new_item && subMenu.new_item === true ? <Chip label="new" className="new-item" color="secondary" /> : ''}
                          </NavLink>
                        </ListItem>
                      </Fragment>
                    )
                  })}
                </List>
              )}
            </Fragment>
          </Collapse>
        </Fragment>
      )
    }
    return (
      <ListItem button component="li" className={`item-solo ${classNames({ 'item-current': this.isCurrent(menu) })}`}>
        <NavLink activeClassName="item-active" to={menu.path}>
          <ListItemIcon className="menu-icon">
            <i className={menu.menu_icon}></i>
          </ListItemIcon>
          <span className="menu">
            <IntlMessages id={menu.menu_title} />
          </span>
        </NavLink>
      </ListItem>
    )
  }
}

export default NavMenuItem
