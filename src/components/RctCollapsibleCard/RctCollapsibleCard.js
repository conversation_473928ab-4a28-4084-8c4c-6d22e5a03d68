/**
 * Rct Collapsible Card
 */
import classnames from 'classnames'
import React, { Component } from 'react'
import { Badge, Collapse } from 'reactstrap'

// rct section loader
import RctSectionLoader from '../RctSectionLoader/RctSectionLoader'

class RctCollapsibleCard extends Component {
  state = {
    reload: false,
    collapsed: !!this.props.isCollapsed,
    close: false,
  }

  componentDidUpdate(prevProps) {
    if (prevProps.isCollapsed !== this.props.isCollapsed) {
      this.setState({ collapsed: !!this.props.isCollapsed })
    }
  }

  onCollapse() {
    this.setState({ collapsed: !this.state.collapsed })
    this.props.onCollapse && this.props.onCollapse(!this.state.collapsed)
  }

  onReload() {
    this.setState({ reload: true })
    let self = this
    setTimeout(() => {
      self.setState({ reload: false })
    }, 1500)
  }

  onCloseSection() {
    this.setState({ close: true })
  }

  getContentHeight() {
    const { fullEditor, contentHeight } = this.props

    if ('contentHeight' in this.props) {
      return contentHeight
    }

    if (fullEditor) {
      try {
        return window.innerHeight - this.refs.contentRef.getBoundingClientRect().y
      } catch (error) {
        return 'auto'
      }
    }

    return 'auto'
  }

  render() {
    const { close, reload, collapsed } = this.state
    const { children, foot, collapsible, closeable, reloadable, thumbnail, heading, fullBlock, fullEditor, badge, postTags, maxTags, tags } =
      this.props
    const { colClasses, customClasses, wrapperClasses, headingClasses, footClasses, contentClasses } = this.props
    const hasTags = Array.isArray(postTags) && !!(postTags || []).length && !!(tags || []).length
    const collapsibleHeading = collapsible && this.props.collapsibleHeading

    return (
      <div className={`${wrapperClasses || ''} ${colClasses || ''} ${classnames('', { 'd-block': !!collapsible && collapsed, fullEditor })}`}>
        <div className={classnames(`rct-block ${customClasses ? customClasses : ''}`, { 'd-none': close, 'mb-0': fullEditor })}>
          {thumbnail && (
            <div className={'rct-block-thumbnail'} onClick={() => collapsibleHeading && this.onCollapse()}>
              {thumbnail}
            </div>
          )}
          {hasTags && (
            <div className={'rct-block-tags'}>
              <div className="d-flex justify-content-end ml-5 overflow-hidden">
                {[...new Set(postTags)].map((tagId, i, arr) => {
                  let tag = tags.find(t => t.ID === tagId)
                  return tag ? (
                    i === (maxTags || 5) && i + 1 < arr.length ? (
                      <span>...</span>
                    ) : i < (maxTags || 5) ? (
                      <Badge key={tag.ID} color="primary" className={`ml-1 px-2 bg-${tag.color || ''}`}>
                        {tag.title}
                      </Badge>
                    ) : null
                  ) : null
                })}
              </div>
            </div>
          )}
          {heading && (
            <div
              className={`rct-block-title ${hasTags ? 'pt-5' : ''} ${headingClasses ? headingClasses : ''}`}
              onClick={() => collapsibleHeading && this.onCollapse()}
            >
              <h4>
                {heading}{' '}
                {badge && (
                  <Badge className="p-1 ml-10" color={badge.class}>
                    {badge.name}
                  </Badge>
                )}{' '}
                {'counter' in this.props && this.props.counter}
              </h4>
              {(collapsible || reloadable || closeable) && (
                <div className="contextual-link">
                  {collapsible && (
                    <a onClick={() => this.onCollapse()}>
                      <i className={(!collapsed && 'ti-arrow-circle-up') || 'ti-arrow-circle-down'}></i>
                    </a>
                  )}
                  {reloadable && (
                    <a onClick={() => this.onReload()}>
                      <i className="ti-reload"></i>
                    </a>
                  )}
                  {closeable && (
                    <a onClick={() => this.onCloseSection()}>
                      <i className="ti-close"></i>
                    </a>
                  )}
                </div>
              )}
            </div>
          )}
          <Collapse isOpen={collapsible ? !collapsed : true}>
            <div
              ref="contentRef"
              className={classnames(contentClasses ? contentClasses : '', { 'rct-block-content': !fullBlock, 'rct-full-block': fullBlock })}
              style={{ minHeight: this.getContentHeight() }}
            >
              {children}
            </div>
          </Collapse>
          {foot && <div className={`rct-block-footer ${footClasses ? footClasses : ''}`}>{foot || ''}</div>}
          {reload && <RctSectionLoader />}
        </div>
      </div>
    )
  }
}

export default RctCollapsibleCard
