import { langMessages } from "Lang/index";

export const HelperGetReports = (props) => {
  const { allData, selectData } = props;

  const dataReturn = [];

  switch (selectData?.name) {
    case "page_impressions":
      dataReturn.push({
        name: "page_impressions",
        sub_title: "Impressões",
        sum: selectData?.sum,
        title: langMessages["reports.total"],
        description: selectData?.description,
      });
      allData.forEach((item) => {
        if (item?.name === "page_impressions_paid") {
          dataReturn.push({
            name: "page_impressions_paid",
            sum: item?.sum,
            title: langMessages["reports.ads"],
            description: item?.description,
          });
        }
        if (item?.name === "page_impressions_organic_v2") {
          dataReturn.push({
            name: "page_impressions_organic_v2",
            sum: item?.sum,
            title: langMessages["reports.organic"],
            description: item?.description,
          });
        }
      });
      break;
    case "page_posts_impressions":
      dataReturn.push({
        name: "page_posts_impressions",
        sub_title: "Impressões de Posts",
        sum: selectData?.sum,
        title: langMessages["reports.total"],
        description: selectData?.description,
      });
      allData.forEach((item) => {
        if (item?.name === "page_posts_impressions_paid") {
          dataReturn.push({
            name: "page_posts_impressions_paid",
            sum: item?.sum,
            title: langMessages["reports.ads"],
          });
        }
        if (item?.name === "page_posts_impressions_organic") {
          dataReturn.push({
            name: "page_posts_impressions_organic",
            sum: item?.sum,
            title: langMessages["reports.organic"],
          });
        }
      });
      break;
    case "page_fan_adds_unique":
      dataReturn.push({
        name: "page_fan_adds_unique",
        sub_title: "Seguidores",
        sum: selectData?.sum,
        title: langMessages["reports.total"],
        description: selectData?.description,
      });
      allData.forEach((item) => {
        if (item?.name === "page_fan_adds") {
          dataReturn.push({
            name: "page_fan_adds",
            sum: item?.sum,
            title: "Seguidores",
          });
        }
        if (item?.name === "page_fan_removes_unique") {
          dataReturn.push({
            name: "page_fan_removes_unique",
            sum: item?.sum,
            title: "Deixaram de Seguir",
          });
        }
        if (item?.name === "page_fans") {
          const index = item?.values?.length - 1;

          dataReturn.push({
            name: "page_fans",
            sum: item?.values[index].value,
            title: "Curtidas na Página",
          });
        }
      });
      break;

    case "page_views_total":
      dataReturn.push({
        description:
          "O número de vezes que o perfil de uma Página foi visualizado por pessoas conectadas e desconectadas.",
      });

      break;
    default:
      break;
  }

  return dataReturn;
};
