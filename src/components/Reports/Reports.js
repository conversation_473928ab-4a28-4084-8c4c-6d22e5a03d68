import RctCollapsibleCard from "Components/RctCollapsibleCard/RctCollapsibleCard";
import React, { useEffect, useState } from "react";
import { HelperGetReports } from "./HelperGetReports";
import ReportsSideDetails from "./ReportsSideDetails";
import ReportsWidgets from "./ReportsWidgets";

import { Avatar, Button, MenuItem, TextField } from "@material-ui/core";
import DateFiltersForm from "Components/DateFilters/DateFiltersForm";
import { getFacebookInfo } from "Helpers/getFacebookInfo";
import { langMessages } from "Lang/index";
import moment from "moment";
import Alert from "reactstrap/lib/Alert";

const Reports = ({ pages }) => {
  const [sanitizedData, setSanitizedData] = useState([]);
  const [reportsCalc, setReportsCalc] = useState({});
  const [choicedId, setChoicedId] = useState("");
  const [choicedToken, setChoicedToken] = useState("");
  const [selectedPage, setSelectedPage] = useState("");
  const [dataPage, setDataPage] = useState({});
  const [errorMsg, setErrorMsg] = useState("");
  const [filters, setFilters] = useState({
    start: moment()
      .subtract(28, "day")
      .valueOf(),
    end: moment().valueOf(),
    range: "last_28d",
  });
  const [customRange, setCustomRange] = useState(false);

  function handleDataPage(dataPage) {
    if (dataPage.hasOwnProperty("data")) {
      const { data } = dataPage;
      const sanitizeData = [];
      data.forEach((item) => {
        let sum = 0;
        let showOnReports = item.hasOwnProperty("showOnReports")
          ? item.showOnReports
          : true;

        if (item.period === "day") {
          item.values.forEach((value) => {
            sum += value.value;
          });
        }
        sanitizeData.push({
          name: item.name,
          sum: sum,
          values: item.values,
          period: item.period,
          title: item.title,
          description: item?.description,
          showOnReports,
        });
      });
      setSanitizedData(sanitizeData);
      return sanitizeData;
    }
  }

  function handleReportsCalc(r) {
    const data = {
      selectData: r,
      allData: sanitizedData,
    };
    const dataReturn = HelperGetReports(data);
    setReportsCalc(dataReturn);
  }

  function handleSetInfoPage(e) {
    const { id, access_token } = e.target?.value;
    setSelectedPage(e.target?.value);
    setChoicedId(id);
    setChoicedToken(access_token);
  }

  function nameField(nameField) {
    return (
      <div className="d-flex flex-row align-items-center">
        <span> {nameField}</span>
      </div>
    );
  }

  function formatData(data) {
    return moment(data).format("YYYY-MM-DD");
  }

  const periodicity_field = {
    today: nameField(langMessages["reports.today"]),
    yesterday: nameField(langMessages["reports.yesterday"]),
    last_3d: nameField(langMessages["reports.last_3d"]),
    last_7d: nameField(langMessages["reports.last_7d"]),
    last_28d: nameField(langMessages["reports.last_28d"]),
    last_90d: nameField(langMessages["reports.last_90d"]),
    this_week_sun_today: nameField(langMessages["reports.this_week_sun_today"]),
    this_month: nameField(langMessages["reports.this_month"]),
    this_year: nameField(langMessages["reports.this_year"]),
    last_week_sun_sat: nameField(langMessages["reports.last_week_sun_sat"]),
    last_month: nameField(langMessages["reports.last_month"]),
    last_year: nameField(langMessages["reports.last_year"]),
  };

  const customOptions = [
    "today",
    "yesterday",
    "last_3d",
    "last_7d",
    "last_28d",
    "last_90d",
    "this_week_sun_today",
    "this_month",
    "this_year",
    "last_week_sun_sat",
    "last_month",
    "last_year",
    "custom",
  ];

  function calculaData(start, end, days) {
    const startDate = moment(start);
    const endDate = moment(end);
    const dias = endDate.diff(startDate, "days");

    if (dias > days) return true;
    return false;
  }

  function isValidaRange(start, end) {
    if (!start || !end) {
      setErrorMsg("Selecione um intervalo de tempo");
      return false;
    }
    if (start > end) {
      setErrorMsg("Data inicial não Pode ser Maior que Data Final");
      return false;
    }
    if (calculaData(start, end, 90)) {
      setErrorMsg("O intervalo de Data deve ser menor ou igual a 90 dias");
      return false;
    }
    setErrorMsg("");
    return true;
  }

  function isValidId_Token(id, access_token) {
    if (!id || !access_token) {
      // setErrorMsg("reports.error");
      setErrorMsg("Selecione uma pagina e/ou filtro por periodo.");
      return false;
    }
    setErrorMsg("");
    return true;
  }

  function handleGetInfoPage() {
    const id = choicedId;
    const access_token = choicedToken;
    const { start, end, range } = filters;

    let since = "";
    let until = "";
    let date_preset = "";

    if (range === "none") {
      setErrorMsg("Selecione um intervalo de tempo");
      return;
    }
    if (range === "custom" || range === "") {
      if (!isValidaRange(start, end)) return;

      since = formatData(start);
      until = formatData(end);
      date_preset = "";
    } else {
      since = "";
      until = "";
      date_preset = range;
    }

    if (!isValidId_Token(id, access_token)) return;

    // period {day, week, days_28, month, lifetime, total_over_range}

    const enumMetrics = [
      {
        name: "page_impressions",
        period: "day",
        title: langMessages["reports.page_impressions"],
        description: langMessages["reports.page_impressions_Description"],
        showOnReports: true,
      },
      {
        name: "page_impressions_organic_v2",
        period: "day",
        title: "page impressions organic v2",
        showOnReports: false,
      },
      {
        name: "page_impressions_paid",
        period: "day",
        title: "page impressions paid",
        showOnReports: false,
      },
      {
        name: "page_views_total",
        period: "day",
        title: langMessages["reports.page_views_total"],
        description: langMessages["reports.page_views_total_Description"],
        showOnReports: true,
      },
      {
        name: "page_posts_impressions",
        period: "day",
        title: langMessages["reports.page_posts_impressions"],
        description: langMessages["reports.page_posts_impressions_Description"],
        showOnReports: true,
      },
      {
        name: "page_posts_impressions_organic",
        period: "day",
        title: "page_posts impressions organic",
        showOnReports: false,
      },
      {
        name: "page_posts_impressions_paid",
        period: "day",
        title: "page_posts impressions paid ",
        showOnReports: false,
      },
      {
        name: "page_fan_adds_unique",
        period: "day",
        title: langMessages["reports.page_fan_adds_unique"],
        description: langMessages["reports.page_fan_adds_unique_Description"],
        showOnReports: true,
      },
      {
        name: "page_fan_adds",
        period: "day",
        title: "page fan adds",
        showOnReports: false,
      },
      {
        name: "page_fan_removes_unique",
        period: "day",
        title: "page_fan removes unique",
        showOnReports: false,
      },
      {
        name: "page_fans",
        period: "day",
        title: "page fan adds",
        showOnReports: false,
      },
    ];

    const period = "day";
    const metrics = enumMetrics.map((metric) => metric.name).join(",");

    getFacebookInfo(
      id,
      access_token,
      metrics,
      date_preset,
      since,
      until,
      period
    ).then((response) => {
      const dataPageResponse = response.data.map((dataItem) => {
        enumMetrics.forEach((metric) => {
          if (dataItem.name === metric.name) {
            dataItem.title = metric.title;
            dataItem.description = metric.description;
            dataItem.showOnReports = metric.showOnReports;
          }
        });
        return dataItem;
      });
      const dataReturn = {
        ...response,
        data: dataPageResponse,
      };
      setDataPage(dataReturn);
    });
  }

  useEffect(() => {
    handleDataPage(dataPage);
  }, [dataPage]);

  function updateState(newState, cb) {
    const { filters } = newState;
    if (filters.range === "custom") {
      setCustomRange(true);
      filters.start = moment()
        .subtract(28, "day")
        .valueOf();
      filters.end = moment().valueOf();
    } else if (filters.range === "") {
      setCustomRange(true);
    } else {
      setCustomRange(false);
    }

    console.log("updateState filters", filters);
    console.log("updateState cb", cb);
    console.log("updateState newState", newState);

    setFilters(filters);
  }

  return (
    <>
      <RctCollapsibleCard
        fullBlock
        colClasses="col-sm-12"
        contentClasses="p-0 overflow-hidden"
        heading={
          <div className="d-flex justify-content-between align-items-center">
            <span>{langMessages["components.facebookReports"]}</span>
          </div>
        }
      >
        <div className="d-flex flex-1 flex-column">
          <div className="d-flex flex-1 flex-row p-10 justify-content-between w-100 align-items-center">
            <TextField
              select
              fullWidth
              size="small"
              variant="outlined"
              style={{ minWidth: 180 }}
              label={langMessages["dates.range"]}
              value={selectedPage}
              onChange={(e) => {
                handleSetInfoPage(e);
              }}
            >

              {pages?.length > 0 ? pages?.map((page) => {
                const { id, name, photos } = page;
                return (
                  <MenuItem value={page} key={id}>
                    <span className="w-100 ">
                      {(photos[0] || {}).source ? (
                        <div className="d-flex flex-row align-items-center">
                          <Avatar
                            src={photos[0].source}
                            className="mr-10"
                            style={{
                              width: 20,
                              height: 20,
                              background: "#fff",
                            }}
                          />
                          <span> {name}</span>
                        </div>
                      ) : (
                        <div className="d-flex flex-row align-items-center">
                          <i className="fa fa-facebook mr-5" />
                          <b> {name}</b>
                        </div>
                      )}
                    </span>
                  </MenuItem>
                );
              }) : <MenuItem value={"none"} key={"none"}>
                    <span className="w-100 ">
                      <div className="d-flex flex-row align-items-center">
                        <i className="fa fa-facebook mr-5" />
                        <span> Não existem páginas associadas a este usuário</span>
                      </div>
                    </span>
                  </MenuItem>}
            </TextField>

            <DateFiltersForm
              filters={filters}
              onUpdate={(filters) => updateState({ filters })}
              options={customOptions}
              custom={customRange}
            />
            <Button

              className="text-white bg-primary"
              onClick={() => handleGetInfoPage()}
            >
              {langMessages["widgets.search"]}
            </Button>
          </div>
          {errorMsg && (
            <div className="d-flex flex-1 p-10 justify-content-between w-100 align-items-center">
              <Alert
                className="mb-15"
                fade={false}
                color="warning"
                isOpen={true}
                toggle={() => setErrorMsg("")}
              >
                {errorMsg}
              </Alert>
            </div>
          )}
        </div>
      </RctCollapsibleCard>

      <RctCollapsibleCard
        fullBlock
        colClasses="col-sm-12"
        contentClasses="p-0 overflow-hidden"
        heading={
          <div className="d-flex justify-content-between align-items-center">
            <span>Relatorios</span>
          </div>
        }
      >
        <div className="d-flex flex-1 flex-column">
          <div className="d-flex flex-column flex-1 ">
            <div className="d-flex flex-row">
              <div className="d-flex flex-column w-70 border-right p-10 ">
                <div>
                  <ReportsWidgets
                    onClick={handleReportsCalc}
                    showGraph
                    data={sanitizedData}
                  />
                </div>
              </div>
              <div className="d-flex flex-column justify-content-between flex-1">
                <div className="d-flex flex-column justify-content-around ">
                  <div className="d-flex align-items-center p-50 justify-content-center mt-20 mb-20 ">
                    <b>Detalhes</b>
                  </div>
                </div>
                <ReportsSideDetails data={reportsCalc} />
              </div>
            </div>
            {/* <div className="d-flex flex-row bg-gray">
          <span>Compartilhamentos</span>
        </div> */}
          </div>
        </div>
      </RctCollapsibleCard>
    </>
  );
};

export default Reports;
