import moment from "moment";
import React from "react";
import { Line } from "react-chartjs-2";

// chart options

export default function ReportsGraph(props) {
  const {
    legend,
    scaleX,
    gridX,
    scaleY,
    gridY,
    responsive,
    title,
    data,
    period,
  } = props;

  let showTitle = false;
  if (title !== undefined) {
    showTitle = true;
  }

  const options = {
    responsive: responsive || true,
    title: {
      display: showTitle,
      text: title,
    },
    legend: {
      display: legend || false,
    },
    scales: {
      xAxes: [
        {
          display: scaleX || true,
          gridLines: {
            display: gridX || false,
          },
        },
      ],
      yAxes: [
        {
          display: scaleY || true,
          gridLines: {
            display: gridY || true,
          },
        },
      ],
    },
  };

  let dataReturn = {};
  let labelsData = [];
  let valuesData = [];
  const valuesDataSet = [];

  function getRandonColor() {
    // Pega um valor aleatorio entre 0 e 255
    // Math.floor(Math.random() * (max - min + 1)) + min;
    return Math.floor(Math.random() * (255 - 1) + 1) + 1;
  }

  data.forEach((item) => {
    if (item.period === "day") {
      const randomC1 = getRandonColor();
      const randomC2 = getRandonColor();
      const randomC3 = getRandonColor();

      if (labelsData.length === 0) {
        item.values.forEach((value) => {
          labelsData.push(moment(value.end_time).format("DD/MM/YYYY"));
          valuesData.push(value.value);
        });
      } else {
        item.values.forEach((value) => {
          valuesData.push(value.value);
        });
      }

      valuesDataSet.push({
        label: item.title,
        data: valuesData,
        borderColor: `rgb(${randomC1}, ${randomC2}, ${randomC3})`,
        backgroundColor: `rgba(${randomC1}, ${randomC2}, ${randomC3}, 0.5)`,
      });
    }
    valuesData = [];
  });

  dataReturn = {
    labels: labelsData,
    datasets: valuesDataSet,
  };

  return <Line data={dataReturn} options={options} />;
}
