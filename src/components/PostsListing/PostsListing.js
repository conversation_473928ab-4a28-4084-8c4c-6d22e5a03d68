/**
 * Project Listing layout
 */
/* eslint-disable */
import React, { Component, Fragment } from 'react'
import { Redirect } from 'react-router-dom'

import { Badge } from 'reactstrap'

// rct card box
import { RctCard, RctCardContent } from 'Components/RctCard'

// rct card box
import ReactTable from 'react-table'
import ReactTableConfig from 'Constants/ReactTableConfig'

// helpers
import { getAppLayout } from 'Helpers/helpers'
import { Avatar, LinearProgress } from '@material-ui/core'

class PostsListing extends Component {
  state = {
    selected: null,
    postId: null,
    pageSize: 10,
    page: 0,
  }

  // get file id
  getFile(Id) {
    this.setState({
      postId: Id,
    })
  }

  render() {
    const { posts, users, columns, module } = this.props
    const { page, pageSize, postId } = this.state

    if (postId !== null) {
      return <Redirect to={`/${module}/${postId}`} />
    }

    let tableData = []

    if (posts && posts.length) {
      tableData = posts.map(post => {
        return {
          id: post.ID || post.id,
          title: post.title || '',
          end: post.end || '',
          start: post.start || '',
          deadline: post.deadline || '',
          sellers: post.sellers || [],
          managers: post.managers || [],
          participants: post.participants || [],
          users: post.users || [],
          thumbnail: post.thumbnail || '',
          status: !!post.ativo || !!post.ativa,
        }
      })
    }

    let columnsConfig = [
      {
        maxWidth: 75,
        Header: 'id',
        accessor: 'id',
      },
      {
        Header: 'Título',
        accessor: 'title',
      },
      {
        maxWidth: 200,
        Header: 'Deadline',
        accessor: 'deadline',
        Cell: props => <Fragment>{props.value && new Date(props.value).toLocaleString()}</Fragment>,
      },
      {
        maxWidth: 200,
        Header: 'Inicio',
        accessor: 'start',
        Cell: props => <Fragment>{props.value && new Date(props.value).toLocaleString()}</Fragment>,
      },
      {
        maxWidth: 200,
        Header: 'Fim',
        accessor: 'end',
        Cell: props => <Fragment>{props.value && new Date(props.value).toLocaleString()}</Fragment>,
      },
      {
        maxWidth: 100,
        Header: 'Participantes',
        accessor: 'participants',
        Cell: props => <Fragment>{(props.value && props.value.length) || 0}</Fragment>,
      },
      {
        maxWidth: 100,
        Header: 'Usuários',
        accessor: 'users',
        Cell: props => <Fragment>{(props.value && props.value.length) || 0}</Fragment>,
      },
      {
        Header: 'status',
        accessor: 'status',
        Cell: props => <Fragment>{props.value === true ? <Badge color="primary">Ativo</Badge> : <Badge color="danger">Inativo</Badge>}</Fragment>,
      },
      {
        Header: 'Vendedores',
        accessor: 'sellers',
        Cell: props => (
          <div className="d-flex">
            {props.value.map((uid, key) => {
              return (
                (users && users[uid] && users[uid].firstName && (
                  <Avatar key={key} title={users[uid].firstName + ' ' + (users[uid].lastName || '')} className="rounded-circle bg-primary mb-5 mr-5">
                    {users[uid].firstName.charAt(0)}
                  </Avatar>
                )) ||
                null
              )
            })}
          </div>
        ),
      },
      {
        Header: 'Gerentes',
        accessor: 'managers',
        Cell: props => (
          <div className="d-flex">
            {props.value.map((uid, key) => {
              return (
                (users && users[uid] && users[uid].firstName && (
                  <Avatar key={key} title={users[uid].firstName + ' ' + (users[uid].lastName || '')} className="rounded-circle bg-primary mb-5 mr-5">
                    {users[uid].firstName.charAt(0)}
                  </Avatar>
                )) ||
                null
              )
            })}
          </div>
        ),
      },
    ]

    if (columns && columns.length) {
      columnsConfig = columnsConfig.filter(c => columns.indexOf(c.accessor) > -1)
    }

    const pages = (posts && Math.ceil(posts.length / pageSize)) || 1

    return (
      <RctCard>
        <RctCardContent>
          <ReactTable
            {...ReactTableConfig}
            data={tableData}
            columns={columnsConfig}
            showPagination={true}
            showPageSizeOptions={true}
            pageSizeOptions={[10, 20, 50, 100]}
            defaultPageSize={pageSize}
            minRows={1}
            page={page}
            pages={pages || -1}
            sortable
            renderPageJump={({ onChange, value, onBlur, onKeyPress, inputType, pageJumpText }) => {
              return (
                <input
                  type={inputType}
                  min="1"
                  max={pages}
                  value={parseInt(page) + 1}
                  onChange={e => this.setState({ page: parseInt(e.target.value) - 1 })}
                />
              )
            }}
            onPageChange={page => this.setState({ page })}
            onPageSizeChange={pageSize => this.setState({ pageSize, page: 0 })}
          />
        </RctCardContent>
      </RctCard>
    )
  }
}
export default PostsListing
