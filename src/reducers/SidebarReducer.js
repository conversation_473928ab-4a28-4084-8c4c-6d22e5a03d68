/**
 * Sidebar Reducers
 */
import {
  AGENCY_TOGGLE_MENU,
  DELETE_DESK_NOTIFICATION,
  DELETE_DESK_NOTIFICATION_FAILURE,
  DELETE_DESK_NOTIFICATION_SUCCESS,
  GET_DESK_NOTIFICATIONS,
  GET_DESK_NOTIFICATIONS_FAILURE,
  GET_DESK_NOTIFICATIONS_SUCCESS,
  LISTEN_DESK_NOTIFICATIONS,
  LISTEN_DESK_NOTIFICATIONS_FAILURE,
  LISTEN_DESK_NOTIFICATIONS_SUCCESS,
  TOGGLE_DESK_NOTIFICATIONS,
  TOGGLE_MENU,
  UPDATE_DESK_NOTIFICATION,
  UPDATE_DESK_NOTIFICATION_FAILURE,
  UPDATE_DESK_NOTIFICATION_SUCCESS,
  UPDATE_DESK_NOTIFICATIONS,
  UPDATE_DESK_NOTIFICATIONS_FAILURE,
  UPDATE_DESK_NOTIFICATIONS_SUCCESS,
} from 'Actions/types'
import update from 'react-addons-update'

// nav links
import agencyNavLinks from 'Components/AgencyMenu/NavLinks'
import navLinks from 'Components/Sidebar/NavLinks'
import { sortByDate } from 'Helpers/helpers'

const INIT_STATE = {
  navLinks,
  agencyNavLinks,
  deskNotifications: [],
  notificationsToggle: false,
  fetchingDeskNotifications: false,
  errorFetchingDeskNotifications: false,
  updatingDeskNotification: false,
  errorUpdatingDeskNotification: false,
  updatingDeskNotifications: false,
  errorUpdatingDeskNotifications: false,
  deletingDeskNotification: false,
  errorDeletingDeskNotification: false,
}

export default (state = INIT_STATE, action) => {
  const noDups = (item, i, arr) => {
    let exists = arr.find((a, k) => k > i && a.ID === item.ID)
    return !exists && !!item.ID
  }

  switch (action.type) {
    case TOGGLE_MENU:
      let index = state.navLinks[action.payload.stateCategory].indexOf(action.payload.menu)
      for (var key in state.navLinks) {
        var obj = state.navLinks[key]
        for (let i = 0; i < obj.length; i++) {
          const element = obj[i]
          if (element.open) {
            if (key === action.payload.stateCategory) {
              return update(state, {
                navLinks: {
                  [key]: {
                    [i]: {
                      open: { $set: false },
                    },
                    [index]: {
                      open: { $set: !action.payload.menu.open },
                    },
                  },
                },
              })
            } else {
              return update(state, {
                navLinks: {
                  [key]: {
                    [i]: {
                      open: { $set: false },
                    },
                  },
                  [action.payload.stateCategory]: {
                    [index]: {
                      open: { $set: !action.payload.menu.open },
                    },
                  },
                },
              })
            }
          }
        }
      }
      return update(state, {
        navLinks: {
          [action.payload.stateCategory]: {
            [index]: {
              open: { $set: !action.payload.menu.open },
            },
          },
        },
      })
    case AGENCY_TOGGLE_MENU:
      let agencyMenuIndex = state.agencyNavLinks[action.payload.stateCategory].indexOf(action.payload.menu)
      for (var id in state.agencyNavLinks) {
        var object = state.agencyNavLinks[id]
        for (let i = 0; i < object.length; i++) {
          const element = object[i]
          if (element.open) {
            if (id === action.payload.stateCategory) {
              return update(state, {
                agencyNavLinks: {
                  [id]: {
                    [i]: {
                      open: { $set: false },
                    },
                    [agencyMenuIndex]: {
                      open: { $set: !action.payload.menu.open },
                    },
                  },
                },
              })
            } else {
              return update(state, {
                agencyNavLinks: {
                  [id]: {
                    [i]: {
                      open: { $set: false },
                    },
                  },
                  [action.payload.stateCategory]: {
                    [agencyMenuIndex]: {
                      open: { $set: !action.payload.menu.open },
                    },
                  },
                },
              })
            }
          }
        }
      }
      return update(state, {
        agencyNavLinks: {
          [action.payload.stateCategory]: {
            [agencyMenuIndex]: {
              open: { $set: !action.payload.menu.open },
            },
          },
        },
      })
    case TOGGLE_DESK_NOTIFICATIONS:
      return { ...state, notificationsToggle: action.payload }

    case LISTEN_DESK_NOTIFICATIONS:
    case GET_DESK_NOTIFICATIONS:
      return { ...state, fetchingDeskNotifications: true, errorFetchingDeskNotifications: false }

    case LISTEN_DESK_NOTIFICATIONS_SUCCESS:
    case GET_DESK_NOTIFICATIONS_SUCCESS:
      var allNotifications = [...state.deskNotifications, ...action.payload]
      return {
        ...state,
        deskNotifications: allNotifications.filter(noDups).sort(sortByDate),
        fetchingDeskNotifications: false,
        errorFetchingDeskNotifications: false,
      }

    case LISTEN_DESK_NOTIFICATIONS_FAILURE:
    case GET_DESK_NOTIFICATIONS_FAILURE:
      return { ...state, fetchingDeskNotifications: false, errorFetchingDeskNotifications: true }

    case UPDATE_DESK_NOTIFICATION:
      return { ...state, updatingDeskNotification: true, errorUpdatingDeskNotification: false }

    case UPDATE_DESK_NOTIFICATION_SUCCESS:
      var updatedNotification = action.payload
      var allUupdatedNotifications = [...state.deskNotifications.filter(n => n.ID !== updatedNotification.ID), updatedNotification]
      return {
        ...state,
        deskNotifications: allUupdatedNotifications.filter(noDups).sort(sortByDate),
        updatingDeskNotification: false,
        errorUpdatingDeskNotification: false,
      }

    case UPDATE_DESK_NOTIFICATION_FAILURE:
      return { ...state, updatingDeskNotification: false, errorUpdatingDeskNotification: true }

    case UPDATE_DESK_NOTIFICATIONS:
      return { ...state, updatingDeskNotifications: true, errorUpdatingDeskNotifications: false }

    case UPDATE_DESK_NOTIFICATIONS_SUCCESS:
      var updatedNotifications = [...state.deskNotifications, ...action.payload]
      return {
        ...state,
        deskNotifications: updatedNotifications.filter(noDups).sort(sortByDate),
        updatingDeskNotifications: false,
        errorUpdatingDeskNotifications: false,
      }

    case UPDATE_DESK_NOTIFICATIONS_FAILURE:
      return { ...state, updatingDeskNotifications: false, errorUpdatingDeskNotifications: true }

    case DELETE_DESK_NOTIFICATION:
      return { ...state, deletingDeskNotification: true, errorDeletingDeskNotification: false }

    case DELETE_DESK_NOTIFICATION_SUCCESS:
      var filteredNotifications = state.deskNotifications.filter(n => n.ID !== action.ID)
      return {
        ...state,
        deskNotifications: filteredNotifications.filter(noDups).sort(sortByDate),
        deletingDeskNotification: false,
        errorDeletingDeskNotification: false,
      }

    case DELETE_DESK_NOTIFICATION_FAILURE:
      return { ...state, deletingDeskNotification: false, errorDeletingDeskNotification: true }

    default:
      return { ...state }
  }
}
