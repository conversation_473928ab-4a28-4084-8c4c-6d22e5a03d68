import { SEGMENTATIONS_COLLECTION_NAME } from "Constants/AppCollections";
import { fetchCollection } from "FirebaseRef/functions";
import { FirebaseRepository } from "FirebaseRef/repository";

const repository = new FirebaseRepository()
const collectionName = SEGMENTATIONS_COLLECTION_NAME
export const getSegmentation = async (segmentationId) => {
    if (!segmentationId) Error('segmentationId is required')

    const path = `/${collectionName}/${segmentationId}`
    return await repository.getDoc(path)
}

export const getSegmentationsByAccountId = async (accountId) => {
    if (!accountId) Error('accountId is required')

    return fetchCollection(collectionName, [['accountId', '==', accountId]]).then(segmentations => {
        return segmentations
    })
}

export async function fetchMultipleSegmentations(ids) {
    return fetchCollection(collectionName, [['id', 'in', ids]]).then(segmentations => {
        return segmentations
    })
}

export async function createSegmentation(segmentation) {
    return repository.addDoc(collectionName, segmentation).then(result => {
        return result
    })
}

export async function updateLeadsOfSegmentation(segmentation, newLeads) {
    console.log('updateLeadsOfSegmentation', segmentation, newLeads)

    const { ID, leads } = segmentation
    const updatedLeads = [...leads, ...newLeads]

    const repository = new FirebaseRepository()
    const path = `${collectionName}/${ID}`

    segmentation = {
        ...segmentation,
        leads: updatedLeads

    }
    return await repository.setDoc(path, segmentation)
}
